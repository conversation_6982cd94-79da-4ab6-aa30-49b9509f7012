RewriteEngine On

# Allow existing files and folders through (but not directories we want to rewrite)
RewriteCond %{REQUEST_FILENAME} -f
RewriteRule ^ - [L]

# Redirect .php extension to clean URLs (this must come first)
RewriteCond %{THE_REQUEST} \s/+(.+?)\.php[\s?] [NC]
RewriteRule ^ /%1? [R=301,L]

# Access control (TEMPORARY - Remove when site goes live)
RewriteRule ^access/?$ access.php [L]

# Handle main pages with clean URLs (specific rules first)
RewriteRule ^about/?$ about.php [L]
RewriteRule ^contact/?$ contact.php [L]
RewriteRule ^industries/?$ industries.php [L]
RewriteRule ^testimonials/?$ testimonials.php [L]
RewriteRule ^privacy-policy/?$ privacy-policy.php [L]
RewriteRule ^terms-of-service/?$ terms-of-service.php [L]
RewriteRule ^consultation-handler/?$ consultation-handler.php [L]
RewriteRule ^contact-handler/?$ contact-handler.php [L]
RewriteRule ^apache-diagnostic/?$ apache-diagnostic.php [L]
RewriteRule ^dropdown-test/?$ dropdown-test.php [L]
RewriteRule ^dropdown-animation-test/?$ dropdown-animation-test.php [L]
RewriteRule ^carousel-test/?$ carousel-test.php [L]
RewriteRule ^carousel-debug/?$ carousel-debug.php [L]
RewriteRule ^wellness-card-test/?$ wellness-card-test.php [L]
RewriteRule ^fixes-verification/?$ fixes-verification.php [L]

# Handle services directory structure
RewriteRule ^services/?$ services/index.php [L]

# Handle specific individual service pages in their category directories
# Software Development
RewriteRule ^services/software-development/web-mobile-development/?$ services/software-development/web-mobile-development.php [L]
RewriteRule ^services/software-development/erp-crm-development/?$ services/software-development/erp-crm-development.php [L]
RewriteRule ^services/software-development/saas-development/?$ services/software-development/saas-development.php [L]

# Cloud & DevOps
RewriteRule ^services/cloud-devops/cloud-migrations/?$ services/cloud-devops/cloud-migrations.php [L]
RewriteRule ^services/cloud-devops/infrastructure-automation/?$ services/cloud-devops/infrastructure-automation.php [L]
RewriteRule ^services/cloud-devops/cicd-pipelines/?$ services/cloud-devops/cicd-pipeline.php [L]

# Cybersecurity
RewriteRule ^services/cybersecurity/managed-security/?$ services/cybersecurity/managed-security.php [L]
RewriteRule ^services/cybersecurity/penetration-testing/?$ services/cybersecurity/penetration-testing.php [L]
RewriteRule ^services/cybersecurity/compliance-consulting/?$ services/cybersecurity/compliance-consulting.php [L]

# Data Analytics
RewriteRule ^services/data-analytics/data-warehousing/?$ services/data-analytics/data-warehousing.php [L]
RewriteRule ^services/data-analytics/bi-dashboards/?$ services/data-analytics/bi-dashboards.php [L]
RewriteRule ^services/data-analytics/etl-elt/?$ services/data-analytics/etl-elt.php [L]
RewriteRule ^services/data-analytics/predictive-analytics/?$ services/data-analytics/predictive-analytics.php [L]

# Managed IT
RewriteRule ^services/managed-it/helpdesk-outsourcing/?$ services/managed-it/it-helpdesk.php [L]
RewriteRule ^services/managed-it/infrastructure-management/?$ services/managed-it/infrastructure-management.php [L]
RewriteRule ^services/managed-it/it-strategy/?$ services/managed-it/it-strategy-audits.php [L]

# Handle individual service pages in subdirectories (only if file exists)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{DOCUMENT_ROOT}/services/$1/$2.php -f
RewriteRule ^services/(software-development|cloud-devops|data-analytics|cybersecurity|managed-it)/([^/]+)/?$ services/$1/$2.php [L]

# Handle service categories - redirect non-slash to slash version
RewriteRule ^services/(software-development|cloud-devops|data-analytics|cybersecurity|managed-it|technology-consulting)$ services/$1/ [R=301,L]

# Handle service categories with trailing slash (main category pages)
RewriteRule ^services/(software-development|cloud-devops|data-analytics|cybersecurity|managed-it|technology-consulting)/?$ services/$1.php [L]

# General rule for any other .php files (fallback)
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME}.php -f
RewriteRule ^(.*)$ $1.php [L]

# Disable directory browsing
Options -Indexes

# Custom error pages
# Use relative path that works in both development and production
ErrorDocument 404 /404.php


# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-Frame-Options "DENY"
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Enable Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache control for static assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>
