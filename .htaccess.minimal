# Minimal .htaccess for testing
# Use this temporarily to test if basic functionality works

RewriteEngine On

# Allow existing files through
RewriteCond %{REQUEST_FILENAME} -f
RewriteRule ^ - [L]

# Simple clean URL rules
RewriteRule ^about/?$ about.php [L]
RewriteRule ^contact/?$ contact.php [L]
RewriteRule ^services/?$ services.php [L]

# Fallback for other .php files
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME}.php -f
RewriteRule ^(.*)$ $1.php [L]
