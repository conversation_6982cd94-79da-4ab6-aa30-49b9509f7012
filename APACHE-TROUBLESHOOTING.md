# Apache 403 Forbidden Error - Troubleshooting Guide

## 🚨 Current Issue
You're getting "403 Forbidden" errors after enabling mod_rewrite, which means Apache is processing .htaccess but there's a permissions issue.

## 🔧 Step-by-Step Fix

### Step 1: Check Your httpd.conf Directory Configuration
Open `C:\Apache24\conf\httpd.conf` and find your directory section. It should look like this:

```apache
<Directory "C:/Apache24/htdocs">
    Options Indexes FollowSymLinks
    AllowOverride All
    Require all granted
</Directory>
```

**CRITICAL**: Make sure you have BOTH:
- `AllowOverride All` (to process .htaccess)
- `Require all granted` (to allow access)

### Step 2: Alternative - Add Specific Directory for Your Project
If the above doesn't work, add this section to your httpd.conf:

```apache
<Directory "C:/Apache24/htdocs/synelogics">
    Options FollowSymLinks
    AllowOverride All
    Require all granted
</Directory>
```

### Step 3: Test with Minimal .htaccess
1. Rename your current .htaccess: `mv .htaccess .htaccess.backup`
2. Rename the minimal version: `mv .htaccess.minimal .htaccess`
3. Restart Apache
4. Test: `http://localhost/synelogics/`

### Step 4: Check Apache Error Logs
Look at your Apache error log for specific error messages:
- Location: `C:\Apache24\logs\error.log`
- Look for recent entries related to your site

### Step 5: Verify Module Loading
Make sure these modules are loaded in httpd.conf:
```apache
LoadModule rewrite_module modules/mod_rewrite.so
LoadModule authz_core_module modules/mod_authz_core.so
```

## 🧪 Quick Tests

### Test 1: Basic Access
```bash
curl -I http://localhost/synelogics/index.php
```
Should return: `HTTP/1.1 200 OK`

### Test 2: Clean URL (after fix)
```bash
curl -I http://localhost/synelogics/about
```
Should return: `HTTP/1.1 200 OK`

### Test 3: PHP Redirect (after fix)
```bash
curl -I http://localhost/synelogics/about.php
```
Should return: `HTTP/1.1 301 Moved Permanently`

## 🔍 Common Issues and Solutions

### Issue: "Options not allowed here"
**Solution**: Your Apache version might not support certain Options in .htaccess. Remove the Options line from .htaccess.

### Issue: "Require not allowed here"
**Solution**: The Require directive might not be allowed in .htaccess. Remove it and ensure it's in httpd.conf instead.

### Issue: Still getting 403 after changes
**Solution**: 
1. Check file permissions on the synelogics folder
2. Make sure Apache service account has read access
3. Try accessing a simple HTML file first

## 📋 Complete Working httpd.conf Section

Here's what your complete directory configuration should look like:

```apache
# Main document root
<Directory "C:/Apache24/htdocs">
    Options Indexes FollowSymLinks
    AllowOverride All
    Require all granted
</Directory>

# Specific configuration for your project (optional but recommended)
<Directory "C:/Apache24/htdocs/synelogics">
    Options FollowSymLinks
    AllowOverride All
    Require all granted
    DirectoryIndex index.php index.html
</Directory>
```

## 🚀 After Fixing

Once you get basic access working:
1. Restore your full .htaccess: `mv .htaccess.backup .htaccess`
2. Test clean URLs: `http://localhost/synelogics/about`
3. Test redirects: `http://localhost/synelogics/about.php`

## 📞 Next Steps

1. Make the httpd.conf changes above
2. Restart Apache
3. Test with: `curl -I http://localhost/synelogics/`
4. If it works, restore full .htaccess
5. Test clean URLs

The key is getting the basic 200 OK response first, then adding the URL rewriting functionality.
