# Synelogics Clean URL Setup Guide

This guide explains how to set up and use the clean URL system for the Synelogics website.

## 🚀 Quick Start

### For Development (PHP Built-in Server)

The website includes a custom PHP router that enables clean URLs when using PHP's built-in development server.

**Windows:**
```bash
# Double-click start-server.bat or run:
start-server.bat
```

**Mac/Linux:**
```bash
# Make the script executable and run:
chmod +x start-server.sh
./start-server.sh

# Or run directly:
php -S localhost:8000 router.php
```

### For Production (Apache/Nginx)

The `.htaccess` file contains all necessary rewrite rules for Apache servers. For Nginx, you'll need to convert these rules to Nginx configuration.

## 📋 Supported Clean URLs

### Main Pages
- `/` → `index.php`
- `/about` → `about.php`
- `/contact` → `contact.php`
- `/industries` → `industries.php`
- `/testimonials` → `testimonials.php`
- `/privacy-policy` → `privacy-policy.php`
- `/terms-of-service` → `terms-of-service.php`

### Services
- `/services` → `services/index.php`
- `/services/software-development` → `services/software-development.php`
- `/services/cloud-devops` → `services/cloud-devops.php`
- `/services/data-analytics` → `services/data-analytics.php`
- `/services/cybersecurity` → `services/cybersecurity.php`
- `/services/managed-it` → `services/managed-it.php`

### Individual Service Pages
- `/services/software-development/web-mobile-development`
- `/services/software-development/erp-crm-development`
- `/services/software-development/saas-development`
- `/services/cloud-devops/cloud-migrations`
- `/services/cloud-devops/infrastructure-automation`
- `/services/cloud-devops/cicd-pipeline`
- `/services/data-analytics/data-warehousing`
- `/services/data-analytics/bi-dashboards`
- `/services/data-analytics/etl-services`
- `/services/data-analytics/predictive-analytics`
- `/services/cybersecurity/managed-security`
- `/services/cybersecurity/penetration-testing`
- `/services/cybersecurity/compliance-consulting`
- `/services/managed-it/helpdesk-outsourcing`
- `/services/managed-it/infrastructure-management`
- `/services/managed-it/it-strategy`

### Form Handlers
- `/contact-handler` → `contact-handler.php`
- `/consultation-handler` → `consultation-handler.php`

## 🧪 Testing

Visit `/url-test` to access a comprehensive testing page that verifies all clean URLs are working correctly.

## 🔧 How It Works

### Development Server (router.php)
The `router.php` file intercepts all requests and:
1. Maps clean URLs to their corresponding PHP files
2. Handles static file serving (CSS, JS, images)
3. Provides 301 redirects for .php URLs to clean URLs
4. Shows 404 page for non-existent routes

### Production Server (.htaccess)
The `.htaccess` file contains Apache mod_rewrite rules that:
1. Redirect .php URLs to clean URLs with 301 status
2. Map clean URLs to their corresponding PHP files
3. Handle the services directory structure
4. Provide fallback rules for any missed files

## 🐛 Troubleshooting

### Clean URLs Not Working?

1. **Development Server**: Make sure you're using the router:
   ```bash
   php -S localhost:8000 router.php
   ```

2. **Production Server**: Ensure mod_rewrite is enabled in Apache and .htaccess files are allowed.

3. **404 Errors**: Check that all referenced PHP files exist in the correct locations.

### Still Getting .php in URLs?

1. Clear your browser cache
2. Check that all internal links use clean URLs (no .php extensions)
3. Verify the router.php or .htaccess rules are being processed

## 📁 File Structure

```
/
├── router.php              # Development server router
├── .htaccess               # Production server rewrite rules
├── start-server.bat        # Windows server start script
├── start-server.sh         # Unix/Linux/Mac server start script
├── index.php               # Homepage
├── about.php               # About page
├── contact.php             # Contact page
├── services/
│   ├── index.php           # Services overview
│   ├── software-development.php
│   ├── cloud-devops.php
│   ├── data-analytics.php
│   ├── cybersecurity.php
│   ├── managed-it.php
│   └── software-development/
│       ├── web-mobile-development.php
│       ├── erp-crm-development.php
│       └── saas-development.php
└── url-test.php            # URL testing page
```

## ✅ Verification

After starting the server, test these URLs:

1. **Clean URLs should work:**
   - http://localhost:8000/about
   - http://localhost:8000/services
   - http://localhost:8000/services/software-development

2. **PHP URLs should redirect:**
   - http://localhost:8000/about.php → http://localhost:8000/about
   - http://localhost:8000/services.php → http://localhost:8000/services

3. **Navigation should be consistent:**
   - All internal links should use clean URLs
   - No .php extensions should be visible in the address bar

## 🎯 Benefits

- **SEO Friendly**: Clean, readable URLs
- **Professional**: No technical file extensions visible
- **Consistent**: Same URL structure regardless of entry point
- **Maintainable**: Easy to add new pages without changing URL structure
- **User Friendly**: Memorable and shareable URLs

---

**Need Help?** Visit `/url-test` for a comprehensive testing interface and troubleshooting guide.
