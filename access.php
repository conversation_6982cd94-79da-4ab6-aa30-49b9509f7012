<?php
session_start();

// Include configuration for proper URL handling
require_once __DIR__ . '/includes/config.php';

// Access code configuration
$ACCESS_CODE = "2350"; // Change this to your desired access code
$SESSION_KEY = "synelogics_access_granted";

// Check if access code is submitted
if ($_POST['access_code'] ?? false) {
    if ($_POST['access_code'] === $ACCESS_CODE) {
        $_SESSION[$SESSION_KEY] = true;
        // Redirect to originally requested page or homepage
        $redirect_url = $_SESSION['redirect_after_access'] ?? url();
        unset($_SESSION['redirect_after_access']);
        header("Location: $redirect_url");
        exit;
    } else {
        $error_message = "Invalid access code. Please try again.";
    }
}

$page_title = "Access Required | Synelogics";
$meta_description = "Synelogics website is currently under development. Please enter the access code to continue.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $meta_description; ?>">
    <link rel="icon" type="image/x-icon" href="<?php echo asset('favicon.ico'); ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom Styles -->
    <style>
        .gradient-bg {
            background: linear-gradient(135deg,rgba(37, 100, 235, 0.95) 0%, #0d9488 50%);
        }
        
        .access-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .floating-animation {
            animation: floating 3s ease-in-out infinite;
        }
        
        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center p-4">
    
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
    </div>
    
    <!-- Access Card -->
    <div class="access-card rounded-3xl shadow-2xl p-8 md:p-12 max-w-md w-full relative z-10">
        
        <!-- Logo and Branding -->
        <div class="text-center mb-8">
            <div class="floating-animation">
                <img src="<?php echo asset('assets/images/logo.png'); ?>" alt="Synelogics Logo" class="w-20 h-20 mx-auto mb-4 rounded-2xl shadow-lg">
            </div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Synelogics</h1>
            <p class="text-gray-600">Technology Solutions</p>
        </div>
        
        <!-- Development Notice -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-medium mb-4">
                <i class="fas fa-tools mr-2"></i>
                Under Development
            </div>
            <h2 class="text-2xl font-semibold text-gray-900 mb-3">Access Required</h2>
            <p class="text-gray-600 leading-relaxed">
                Our website is currently under development. Please enter the access code to continue to the site.
            </p>
        </div>
        
        <!-- Access Form -->
        <form method="POST" class="space-y-6">
            <div>
                <label for="access_code" class="block text-sm font-medium text-gray-700 mb-2">
                    Access Code
                </label>
                <div class="relative">
                    <input 
                        type="password" 
                        id="access_code" 
                        name="access_code" 
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        placeholder="Enter access code"
                        required
                        autocomplete="off"
                    >
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <i class="fas fa-lock text-gray-400"></i>
                    </div>
                </div>
                
                <?php if (isset($error_message)): ?>
                <div class="mt-2 text-red-600 text-sm flex items-center">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
                <?php endif; ?>
            </div>
            
            <button
                type="submit"
                class="w-full bg-gradient-to-r from-blue-600 to-teal-600 text-white py-3 px-6 rounded-lg font-semibold hover:from-blue-700 hover:to-teal-700 transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
                <i class="fas fa-arrow-right mr-2"></i>
                Access Website
            </button>
        </form>
        
        <!-- Contact Information -->
        <div class="mt-8 pt-6 border-t border-gray-200 text-center">
            <p class="text-sm text-gray-600 mb-2">Need access? Contact us:</p>
            <a href="mailto:<EMAIL>" class="text-teal-600 hover:text-teal-700 text-sm font-medium">
                <i class="fas fa-envelope mr-1"></i>
                <EMAIL>
            </a>
        </div>
        
        <!-- Footer -->
        <div class="mt-6 text-center">
            <p class="text-xs text-gray-500">
                © <?php echo date('Y'); ?> Synelogics. All rights reserved.
            </p>
        </div>
    </div>
    
    <!-- Floating Elements -->
    <div class="absolute top-10 left-10 w-4 h-4 bg-white bg-opacity-20 rounded-full pulse-animation"></div>
    <div class="absolute top-1/4 right-10 w-6 h-6 bg-white bg-opacity-15 rounded-full pulse-animation" style="animation-delay: 1s;"></div>
    <div class="absolute bottom-20 left-1/4 w-3 h-3 bg-white bg-opacity-25 rounded-full pulse-animation" style="animation-delay: 2s;"></div>
    <div class="absolute bottom-1/3 right-1/4 w-5 h-5 bg-white bg-opacity-10 rounded-full pulse-animation" style="animation-delay: 0.5s;"></div>
    
</body>
</html>
