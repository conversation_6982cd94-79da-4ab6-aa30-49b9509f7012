<?php
/**
 * Apache Configuration Diagnostic Tool
 * This script helps diagnose Apache configuration issues
 */

$page_title = "Apache Configuration Diagnostic | Synelogics";
$meta_description = "Diagnostic tool for Apache configuration";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
        h1, h2 { color: #333; }
        .btn { display: inline-block; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Apache Configuration Diagnostic</h1>
        
        <div class="test-section">
            <h2>1. PHP Configuration Check</h2>
            <?php
            echo "<div class='info'>";
            echo "<strong>PHP Version:</strong> " . PHP_VERSION . "<br>";
            echo "<strong>Server Software:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
            echo "<strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
            echo "<strong>Current Script:</strong> " . $_SERVER['SCRIPT_NAME'] . "<br>";
            echo "</div>";
            ?>
        </div>

        <div class="test-section">
            <h2>2. mod_rewrite Detection</h2>
            <?php
            if (function_exists('apache_get_modules')) {
                $modules = apache_get_modules();
                if (in_array('mod_rewrite', $modules)) {
                    echo "<div class='success'>✅ mod_rewrite is ENABLED</div>";
                } else {
                    echo "<div class='error'>❌ mod_rewrite is NOT ENABLED</div>";
                    echo "<div class='warning'>You need to enable mod_rewrite in httpd.conf</div>";
                }
            } else {
                echo "<div class='warning'>⚠️ Cannot detect mod_rewrite status (function not available)</div>";
            }
            ?>
        </div>

        <div class="test-section">
            <h2>3. .htaccess File Check</h2>
            <?php
            $htaccess_path = __DIR__ . '/.htaccess';
            if (file_exists($htaccess_path)) {
                echo "<div class='success'>✅ .htaccess file exists</div>";
                echo "<div class='info'><strong>File size:</strong> " . filesize($htaccess_path) . " bytes</div>";
                echo "<div class='info'><strong>Last modified:</strong> " . date('Y-m-d H:i:s', filemtime($htaccess_path)) . "</div>";
            } else {
                echo "<div class='error'>❌ .htaccess file NOT found</div>";
            }
            ?>
        </div>

        <div class="test-section">
            <h2>4. URL Rewriting Test</h2>
            <?php
            $current_url = $_SERVER['REQUEST_URI'];
            echo "<div class='info'><strong>Current URL:</strong> $current_url</div>";
            
            if (strpos($current_url, '.php') !== false) {
                echo "<div class='warning'>⚠️ You accessed this page with .php extension</div>";
                echo "<div class='info'>Try accessing: <a href='/synelogics/apache-diagnostic'>/synelogics/apache-diagnostic</a></div>";
            } else {
                if (basename($current_url) === 'apache-diagnostic') {
                    echo "<div class='success'>✅ Clean URL is working! (.htaccess is being processed)</div>";
                } else {
                    echo "<div class='info'>Access this page via clean URL to test rewriting</div>";
                }
            }
            ?>
        </div>

        <div class="test-section">
            <h2>5. Quick URL Tests</h2>
            <p>Click these links to test URL rewriting:</p>
            <a href="/synelogics/" class="btn">Home</a>
            <a href="/synelogics/about" class="btn">About (Clean)</a>
            <a href="/synelogics/about.php" class="btn">About.php (Should Redirect)</a>
            <a href="/synelogics/contact" class="btn">Contact (Clean)</a>
            <a href="/synelogics/services" class="btn">Services (Clean)</a>
        </div>

        <div class="test-section">
            <h2>6. Server Environment</h2>
            <pre><?php
            echo "REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD'] . "\n";
            echo "REQUEST_URI: " . $_SERVER['REQUEST_URI'] . "\n";
            echo "SCRIPT_NAME: " . $_SERVER['SCRIPT_NAME'] . "\n";
            echo "QUERY_STRING: " . ($_SERVER['QUERY_STRING'] ?? 'none') . "\n";
            echo "HTTP_HOST: " . $_SERVER['HTTP_HOST'] . "\n";
            echo "SERVER_NAME: " . $_SERVER['SERVER_NAME'] . "\n";
            echo "SERVER_PORT: " . $_SERVER['SERVER_PORT'] . "\n";
            ?></pre>
        </div>

        <div class="test-section">
            <h2>7. Configuration Instructions</h2>
            <div class="warning">
                <strong>If clean URLs are not working:</strong>
                <ol>
                    <li>Open your Apache httpd.conf file</li>
                    <li>Uncomment: <code>LoadModule rewrite_module modules/mod_rewrite.so</code></li>
                    <li>Change <code>AllowOverride None</code> to <code>AllowOverride All</code> in your directory section</li>
                    <li>Restart Apache</li>
                    <li>Test again</li>
                </ol>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <?php
            // Include config for proper URL handling
            require_once 'includes/config.php';
            ?>
            <a href="<?php echo url(); ?>" class="btn">← Back to Homepage</a>
            <a href="<?php echo url('url-test'); ?>" class="btn">URL Test Page</a>
        </div>
    </div>
</body>
</html>
