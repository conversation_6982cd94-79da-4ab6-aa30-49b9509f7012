/* Custom CSS for Synelogics Website */

/* Reset and base styles */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 15px;
    /* Reduced from 16px for better zoom scaling */
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.5;
    /* Reduced from 1.6 for tighter spacing */
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    color: #1f2937;
    background-color: #ffffff;
}

/* Container improvements - Optimized for zoom levels 90%-125% */
.container-custom {
    max-width: 1140px;
    /* Reduced from 1200px */
    margin: 0 auto;
    padding: 0 0.875rem;
    /* Reduced from 1rem */
}

@media (min-width: 640px) {
    .container-custom {
        padding: 0 1.25rem;
        /* Reduced from 1.5rem */
    }
}

@media (min-width: 1024px) {
    .container-custom {
        padding: 0 1.75rem;
        /* Reduced from 2rem */
    }
}

@media (min-width: 1280px) {
    .container-custom {
        max-width: 1200px;
        /* Slightly larger on very wide screens */
        padding: 0 2rem;
    }
}

/* Section spacing improvements - Optimized for zoom scaling */
.section-padding {
    padding: 3.5rem 0;
    /* Reduced from 4rem */
}

@media (min-width: 768px) {
    .section-padding {
        padding: 4.5rem 0;
        /* Reduced from 5rem */
    }
}

@media (min-width: 1024px) {
    .section-padding {
        padding: 5.5rem 0;
        /* Reduced from 6rem */
    }
}

/* Mobile-first responsive improvements - Optimized for zoom scaling */
@media (max-width: 767px) {
    .section-padding {
        padding: 2.75rem 0;
        /* Reduced from 3rem */
    }

    .btn-primary,
    .btn-outline,
    .btn-secondary {
        width: 100%;
        text-align: center;
        padding: 0.75rem 1.25rem;
        /* Optimized padding */
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        /* Smaller font on mobile */
    }

    .grid {
        gap: 0.875rem;
        /* Reduced gap */
    }

    .text-hero {
        font-size: 1.875rem;
        /* Smaller hero text on mobile */
        line-height: 1.1;
    }

    .text-section-title {
        font-size: 1.5rem;
        /* Smaller section titles on mobile */
        line-height: 1.2;
    }

    .container-custom {
        padding: 0 0.75rem;
        /* Reduced mobile padding */
    }

    /* Mobile navigation improvements */
    .mobile-menu-open {
        overflow: hidden;
    }

    /* Service cards mobile optimization */
    .service-card {
        margin-bottom: 1.25rem;
        /* Reduced margin */
    }

    /* Form improvements for mobile */
    .form-input,
    .form-textarea {
        font-size: 16px;
        /* Prevents zoom on iOS */
    }

    /* Button improvements */
    .btn-primary,
    .btn-outline {
        width: 100%;
        text-align: center;
        padding: 0.875rem 1.5rem;
    }

    /* Grid improvements for mobile */
    .grid {
        gap: 1rem;
    }
}



.grid {
    gap: 1.5rem;
}

/* Tablet-specific service card adjustments */
.service-card {
    padding: 1.5rem;
}


/* Image optimization and responsiveness */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

.img-responsive {
    width: 100%;
    height: auto;
    object-fit: cover;
}

/* Placeholder for missing images */
.img-placeholder {
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    font-size: 0.875rem;
    min-height: 200px;
    border-radius: 0.5rem;
}

/* Service detail page improvements */
.service-detail-sidebar {
    position: sticky;
    top: 6rem;
}

@media (max-width: 1023px) {
    .service-detail-sidebar {
        position: static;
        margin-bottom: 2rem;
    }
}

/* Consultation form mobile improvements */
@media (max-width: 767px) {
    .consultation-form .grid {
        grid-template-columns: 1fr;
    }

    .consultation-form .form-input,
    .consultation-form .form-textarea {
        margin-bottom: 1rem;
    }
}

/* Services dropdown - ensure perfect centering */
.services-dropdown {
    position: absolute !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    transform-origin: center center !important;
}

/* Navigation mega menu mobile improvements */
@media (max-width: 1023px) {
    .services-dropdown {
        position: static !important;
        transform: none !important;
        width: 100% !important;
        max-width: none !important;
        margin-top: 0 !important;
        box-shadow: none !important;
        border: none !important;
        border-radius: 0 !important;
    }

    .services-dropdown .grid {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
    }
}

@media (min-width: 1024px) {
    .section-padding {
        padding: 6rem 0;
    }
}

/* Custom animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

/* Animation classes */
.animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.8s ease-out;
}

.animate-fade-in-right {
    animation: fadeInRight 0.8s ease-out;
}

.animate-pulse-hover:hover {
    animation: pulse 0.6s ease-in-out;
}

/* Custom gradient backgrounds */
.gradient-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #14b8a6 100%);
}

.gradient-secondary {
    background: linear-gradient(135deg, #14b8a6 0%, #06b6d4 100%);
}

/* Custom button styles - Optimized for zoom scaling */
.btn-primary {
    background-color: #2563eb;
    color: white;
    padding: 0.625rem 1.25rem;
    /* Reduced from 0.75rem 1.5rem */
    border-radius: 0.5rem;
    font-weight: 500;
    font-size: 0.9rem;
    /* Slightly smaller font */
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: none;
    cursor: pointer;
}

.btn-primary:hover {
    background-color: #1d4ed8;
    transform: translateY(-1px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    color: white;
    text-decoration: none;
}

.btn-secondary {
    background-color: #059669;
    color: white;
    padding: 0.625rem 1.25rem;
    /* Reduced from 0.75rem 1.5rem */
    border-radius: 0.5rem;
    font-weight: 500;
    font-size: 0.9rem;
    /* Slightly smaller font */
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: none;
    cursor: pointer;
}

.btn-secondary:hover {
    background-color: #047857;
    transform: translateY(-1px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    color: white;
    text-decoration: none;
}

.btn-outline {
    border: 2px solid #2563eb;
    color: #2563eb;
    background-color: transparent;
    padding: 0.625rem 1.25rem;
    /* Reduced from 0.75rem 1.5rem */
    border-radius: 0.5rem;
    font-weight: 500;
    font-size: 0.9rem;
    /* Slightly smaller font */
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-outline:hover {
    background-color: #2563eb;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

/* Section separators and headers */
.section-separator {
    height: 1px;
    background: linear-gradient(to right, transparent, #e5e7eb, transparent);
    margin: 2rem 0;
}

.section-header {
    position: relative;
    padding-bottom: 1rem;
    margin-bottom: 2rem;
}

.section-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(to right, #2563eb, #059669);
    border-radius: 2px;
}

.section-header.left-aligned::after {
    left: 0;
    transform: none;
}

/* Enhanced section styling */
.section-padding {
    position: relative;
}

.section-padding::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(229, 231, 235, 0.5), transparent);
}

/* Industries Carousel Styles */
.industries-carousel {
    position: relative;
    overflow: hidden;
    padding: 0 60px;
    /* Ensure container can accommodate all slides at low zoom levels */
    min-width: 100%;
}

.industries-carousel-container {
    display: flex;
    transition: transform 0.5s ease-in-out;
    gap: 1.5rem;
}

.industry-card {
    flex: 0 0 auto;
    width: 280px;
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.industry-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.15);
}

.carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 10;
}

.carousel-nav:hover {
    background: #2563eb;
    color: white;
    transform: translateY(-50%) scale(1.1);
}

.carousel-nav.prev {
    left: 10px;
}

.carousel-nav.next {
    right: 10px;
}

.carousel-nav:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f3f4f6;
}

.carousel-nav:disabled:hover {
    transform: translateY(-50%);
    background: #f3f4f6;
    color: #6b7280;
}

/* Carousel dots removed - using infinite carousel without dots */

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .industries-carousel {
        padding: 0 35px;
        /* Reduced padding for mobile */
    }

    .industry-card {
        width: 220px;
        /* Smaller cards for mobile */
        padding: 1.25rem;
        /* Reduced padding */
    }

    .carousel-nav {
        width: 36px;
        /* Smaller nav buttons */
        height: 36px;
        font-size: 0.875rem;
    }

    .carousel-nav.prev {
        left: 2px;
    }

    .carousel-nav.next {
        right: 2px;
    }
}

@media (max-width: 480px) {
    .industries-carousel {
        padding: 0 30px;
    }

    .industry-card {
        width: 200px;
        /* Even smaller for very small screens */
        padding: 1rem;
    }

    .industry-card h4 {
        font-size: 1rem;
        /* Smaller title */
        margin-bottom: 0.5rem;
    }

    .industry-card p {
        font-size: 0.875rem;
        /* Smaller description */
    }
}

/* Card hover effects */
.card-hover {
    @apply transition-all duration-300 transform hover:scale-105 hover:shadow-xl;
}

/* Service card styles */
.service-card {
    @apply bg-white rounded-xl shadow-lg p-6 transition-all duration-300 hover:shadow-2xl hover:transform hover:scale-105 border border-gray-100;
}

/* Icon styles */
.icon-primary {
    @apply text-primary-600;
}

.icon-secondary {
    @apply text-secondary-600;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #3b82f6;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #2563eb;
}

/* Loading animation */
.loading-spinner {
    border: 3px solid #f3f4f6;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Form styles */
.form-input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors;
}

.form-textarea {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors resize-vertical;
}

.form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
}

/* Hero section styles - Optimized for better visual appeal */
.hero-gradient {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.12) 0%, rgba(20, 184, 166, 0.12) 100%);
    padding: 3rem 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

@media (min-width: 768px) {
    .hero-gradient {
        padding: 3.5rem 0;
    }
}

@media (min-width: 1024px) {
    .hero-gradient {
        padding: 4rem 0;
    }
}

/* Hero content enhancements */
.hero-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #1e40af;
    margin-bottom: 1.5rem;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.hero-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.hero-badge i {
    margin-right: 0.5rem;
    color: #3b82f6;
}

/* Hero stats styling */
.hero-content .flex.flex-wrap {
    opacity: 0.8;
}

.hero-content .flex.flex-wrap .flex.items-center {
    transition: all 0.3s ease;
}

.hero-content .flex.flex-wrap .flex.items-center:hover {
    opacity: 1;
    transform: translateY(-1px);
}

/* Testimonial styles */
.testimonial-card {
    @apply bg-white rounded-xl shadow-lg p-8 relative;
}

.testimonial-card::before {
    content: '"';
    @apply absolute top-4 left-6 text-6xl text-primary-200 font-serif;
}

/* Industry card styles */
.industry-card {
    @apply bg-gradient-to-br from-white to-gray-50 rounded-xl shadow-lg p-6 text-center transition-all duration-300 hover:shadow-2xl hover:transform hover:scale-105;
}

/* Stats counter styles */
.stats-counter {
    @apply text-4xl font-bold text-primary-600 mb-2;
}

/* Note: Section spacing and container styles are defined at the top of the file */

/* Zoom level optimizations */
@media screen and (min-resolution: 120dpi) {

    /* Optimize for high DPI displays and zoom levels */
    body {
        font-size: 14px;
        /* Slightly smaller for high zoom */
    }

    .container-custom {
        max-width: 1080px;
        /* Smaller container for high zoom */
    }
}

@media screen and (max-resolution: 96dpi) {

    /* Optimize for standard DPI and lower zoom levels */
    body {
        font-size: 15px;
        /* Standard size */
    }
}

/* Responsive text sizes - Optimized for zoom scaling */
.text-hero {
    font-size: 2.25rem;
    /* 36px - reduced from text-4xl */
    font-weight: 700;
    line-height: 1.2;
}

.text-section-title {
    font-size: 1.875rem;
    /* 30px - reduced from text-3xl */
    font-weight: 700;
    line-height: 1.25;
}

.text-subsection-title {
    font-size: 1.5rem;
    /* 24px - reduced from text-2xl */
    font-weight: 600;
    line-height: 1.3;
}

@media (min-width: 768px) {
    .text-hero {
        font-size: 2.75rem;
        /* 44px - reduced from text-5xl */
    }

    .text-section-title {
        font-size: 2.25rem;
        /* 36px - reduced from text-4xl */
    }

    .text-subsection-title {
        font-size: 1.75rem;
        /* 28px - reduced from text-3xl */
    }
}

@media (min-width: 1024px) {
    .text-hero {
        font-size: 3.25rem;
        /* 52px - reduced from text-6xl */
    }

    .text-section-title {
        font-size: 2.75rem;
        /* 44px - reduced from text-5xl */
    }

    .text-subsection-title {
        font-size: 2rem;
        /* 32px - same as text-2xl */
    }
}

/* Utility classes */
.text-gradient {
    background: linear-gradient(135deg, #3b82f6 0%, #14b8a6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.border-gradient {
    border-image: linear-gradient(135deg, #3b82f6 0%, #14b8a6 100%) 1;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .mobile-center {
        text-align: center;
    }

    .mobile-full-width {
        width: 100%;
    }

    .mobile-stack {
        flex-direction: column;
    }
}

/* Performance optimizations */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

/* Improved focus states for accessibility */
.focus-visible:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Better image loading */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

.lazy-load {
    opacity: 0;
    transition: opacity 0.3s;
}

.lazy-load.loaded {
    opacity: 1;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
    }

    .container-custom {
        max-width: none;
        padding: 0;
    }
}