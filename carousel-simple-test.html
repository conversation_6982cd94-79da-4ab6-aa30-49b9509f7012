<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Carousel Test</title>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <style>
        .test-carousel {
            position: relative;
            overflow: hidden;
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            border: 2px solid #ccc;
            padding: 20px;
        }
        .test-carousel-container {
            display: flex;
            transition: transform 0.5s ease-in-out;
            gap: 20px;
        }
        .test-card {
            flex: 0 0 auto;
            width: 200px;
            height: 150px;
            background: linear-gradient(45deg, #3b82f6, #14b8a6);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        .test-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0,0,0,0.5);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
        }
        .test-nav.prev { left: 10px; }
        .test-nav.next { right: 10px; }
        .debug-info {
            margin: 20px 0;
            padding: 15px;
            background: #f0f0f0;
            border-radius: 5px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>Simple Carousel Test</h1>
    
    <div class="debug-info" id="debug-info">
        Loading...
    </div>
    
    <div class="test-carousel" x-data="{
        currentSlide: 0,
        slideWidth: 220,
        totalSlides: 5,
        autoAdvanceInterval: null,
        
        init() {
            console.log('🎠 Simple Test Carousel Init');
            this.startAutoAdvance();
            this.updateDebug();
        },
        
        startAutoAdvance() {
            console.log('🎠 Starting auto-advance...');
            this.autoAdvanceInterval = setInterval(() => {
                console.log('🎠 Auto-advance tick, current:', this.currentSlide);
                this.nextSlide();
            }, 2000);
        },
        
        nextSlide() {
            console.log('🎠 Next slide called, current:', this.currentSlide);
            this.currentSlide++;
            if (this.currentSlide >= this.totalSlides) {
                this.currentSlide = 0;
            }
            console.log('🎠 New slide:', this.currentSlide);
            this.updateDebug();
        },
        
        prevSlide() {
            console.log('🎠 Prev slide called, current:', this.currentSlide);
            this.currentSlide--;
            if (this.currentSlide < 0) {
                this.currentSlide = this.totalSlides - 1;
            }
            console.log('🎠 New slide:', this.currentSlide);
            this.updateDebug();
        },
        
        updateDebug() {
            document.getElementById('debug-info').innerHTML = `
                Current Slide: ${this.currentSlide}<br>
                Total Slides: ${this.totalSlides}<br>
                Slide Width: ${this.slideWidth}px<br>
                Transform: translateX(-${this.currentSlide * this.slideWidth}px)<br>
                Auto-advance: ${this.autoAdvanceInterval ? 'Running' : 'Stopped'}
            `;
        }
    }">
        <button class="test-nav prev" @click="prevSlide()">‹</button>
        
        <div class="test-carousel-container" :style="`transform: translateX(-${currentSlide * slideWidth}px)`">
            <div class="test-card">Card 1</div>
            <div class="test-card">Card 2</div>
            <div class="test-card">Card 3</div>
            <div class="test-card">Card 4</div>
            <div class="test-card">Card 5</div>
        </div>
        
        <button class="test-nav next" @click="nextSlide()">›</button>
    </div>
    
    <div style="margin-top: 20px; text-align: center;">
        <button onclick="testCarousel.nextSlide()" style="margin: 5px; padding: 10px;">Manual Next</button>
        <button onclick="testCarousel.prevSlide()" style="margin: 5px; padding: 10px;">Manual Prev</button>
        <button onclick="console.clear()" style="margin: 5px; padding: 10px;">Clear Console</button>
    </div>
    
    <script>
        // Make carousel accessible globally for testing
        let testCarousel = null;
        
        document.addEventListener('alpine:init', () => {
            console.log('🎠 Alpine.js initialized');
        });
        
        setTimeout(() => {
            const carouselEl = document.querySelector('.test-carousel');
            if (carouselEl && carouselEl._x_dataStack) {
                testCarousel = carouselEl._x_dataStack[0];
                console.log('✅ Test carousel found and accessible');
            } else {
                console.log('❌ Test carousel not found');
            }
        }, 1000);
    </script>
</body>
</html>
