<?php
$page_title = "Carousel Functionality Test | Synelogics";
$meta_description = "Testing carousel dot indicators and functionality";
include 'includes/header.php';
?>

<section class="section-padding bg-white">
    <div class="container-custom">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-4xl font-bold text-gray-900 mb-8">🎠 Carousel Functionality Test</h1>
            
            <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
                <h2 class="text-xl font-semibold text-green-900 mb-4">✅ Industries Carousel - INFINITE CAROUSEL IMPLEMENTED</h2>
                <p class="text-green-800 mb-4">
                    Industries carousel has been completely redesigned as an infinite auto-advancing carousel.
                </p>
                <ul class="text-green-800 space-y-2">
                    <li>• <strong>Infinite Loop:</strong> Seamlessly loops from last card (Wellness) back to first (Healthcare)</li>
                    <li>• <strong>Auto-Advance:</strong> Automatically advances every 2 seconds</li>
                    <li>• <strong>Hover Pause:</strong> Auto-advance pauses when hovering over carousel</li>
                    <li>• <strong>No Dot Indicators:</strong> Removed dots for cleaner, simpler interface</li>
                    <li>• <strong>Arrow Navigation:</strong> Manual navigation still available with left/right arrows</li>
                    <li>• <strong>All Cards Visible:</strong> Fixed container width to show all 10 industry cards properly</li>
                </ul>
            </div>
            
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                <h2 class="text-xl font-semibold text-blue-900 mb-4">🧪 Test Instructions</h2>
                <ol class="text-blue-800 space-y-2 list-decimal list-inside">
                    <li><strong>Visit Homepage:</strong> Go to <a href="<?php echo url(''); ?>" class="underline">homepage</a></li>
                    <li><strong>Industries Carousel:</strong> Scroll to "Industries We Serve" section</li>
                    <li><strong>Test Auto-Advance:</strong> Watch carousel automatically advance every 2 seconds</li>
                    <li><strong>Test Infinite Loop:</strong> Watch it go from Wellness → Healthcare seamlessly</li>
                    <li><strong>Test Hover Pause:</strong> Hover over carousel to pause auto-advance</li>
                    <li><strong>Test Arrow Navigation:</strong> Click left/right arrows for manual control</li>
                    <li><strong>Testimonials Carousel:</strong> Scroll to "What Our Clients Say" section (still has dots)</li>
                </ol>
            </div>

            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
                <h2 class="text-xl font-semibold text-yellow-900 mb-4">🔧 Technical Implementation</h2>
                <div class="text-yellow-800 space-y-4">
                    <div>
                        <h3 class="font-semibold">Industries Carousel (Alpine.js - Infinite):</h3>
                        <pre class="bg-yellow-100 p-3 rounded text-sm mt-2"><code>// Infinite carousel implementation
nextSlide() {
    this.currentSlide++;
    if (this.currentSlide >= this.totalSlides) {
        this.currentSlide = 0; // Loop back to start
    }
}

// Auto-advance every 2 seconds
startAutoAdvance() {
    this.autoAdvanceInterval = setInterval(() => {
        this.nextSlide();
    }, 2000);
}</code></pre>
                    </div>
                    <div>
                        <h3 class="font-semibold">Testimonials Carousel (JavaScript):</h3>
                        <pre class="bg-yellow-100 p-3 rounded text-sm mt-2"><code>// 3 slides with dot navigation
// Auto-advance every 5 seconds
// Dot click navigation functionality</code></pre>
                    </div>
                </div>
            </div>

            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">📋 Expected Behavior</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-semibold text-gray-800 mb-2">Industries Carousel (Infinite):</h3>
                        <ul class="text-gray-700 space-y-1 text-sm">
                            <li>• Auto-advances every 2 seconds continuously</li>
                            <li>• Infinite loop: Wellness → Healthcare seamlessly</li>
                            <li>• Pauses on hover, resumes on mouse leave</li>
                            <li>• Arrow navigation works in both directions</li>
                            <li>• No dots - clean, minimal interface</li>
                            <li>• All 10 cards fully visible when displayed</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-800 mb-2">Testimonials Carousel:</h3>
                        <ul class="text-gray-700 space-y-1 text-sm">
                            <li>• Auto-advances every 5 seconds</li>
                            <li>• 3 slides with 3 testimonials each</li>
                            <li>• Dots show current slide (blue = active)</li>
                            <li>• Clicking dots jumps to specific slide</li>
                            <li>• Smooth fade transitions between slides</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="text-center mt-8">
                <a href="<?php echo url(''); ?>" class="btn-primary mr-4">Test on Homepage</a>
                <a href="<?php echo url('fixes-verification'); ?>" class="btn-outline">View All Fixes</a>
            </div>
        </div>
    </div>
</section>

<script>
// Test carousel functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎠 Carousel Test Page Loaded');
    
    // Test if Alpine.js industriesCarousel function is available
    if (typeof industriesCarousel === 'function') {
        console.log('✅ industriesCarousel function is available');
        
        // Test the function
        const testCarousel = industriesCarousel();
        testCarousel.updateSlidesPerView(); // Initialize responsive settings

        console.log('Industries Carousel Test (Infinite):', {
            totalSlides: testCarousel.totalSlides,
            slidesPerView: testCarousel.slidesPerView,
            slideWidth: testCarousel.slideWidth,
            currentSlide: testCarousel.currentSlide,
            autoAdvanceInterval: testCarousel.autoAdvanceInterval !== null,
            windowWidth: window.innerWidth
        });

        // Test infinite loop logic
        console.log('Infinite Loop Test:');
        console.log('- Current slide:', testCarousel.currentSlide);
        console.log('- Next slide would be:', (testCarousel.currentSlide + 1) % testCarousel.totalSlides);
        console.log('- Previous slide would be:', testCarousel.currentSlide === 0 ? testCarousel.totalSlides - 1 : testCarousel.currentSlide - 1);

        // Test auto-advance
        if (testCarousel.autoAdvanceInterval) {
            console.log('✅ Auto-advance is active (2 second intervals)');
        } else {
            console.log('❌ Auto-advance is not active');
        }
    } else {
        console.log('❌ industriesCarousel function not found');
    }
    
    // Test testimonial slider elements
    const testimonialSlider = document.querySelector('.testimonial-slider');
    if (testimonialSlider) {
        const slides = testimonialSlider.querySelectorAll('.testimonial-slide');
        const dots = testimonialSlider.querySelectorAll('.slider-dot');
        
        console.log('Testimonials Carousel Test:', {
            totalSlides: slides.length,
            totalDots: dots.length,
            slidesMatch: slides.length === dots.length
        });
        
        if (slides.length === dots.length) {
            console.log('✅ Testimonials carousel setup is correct');
        } else {
            console.log('⚠️ Testimonials carousel slides/dots mismatch');
        }
    } else {
        console.log('❌ Testimonial slider not found on this page');
    }
});
</script>

<?php include 'includes/footer.php'; ?>
