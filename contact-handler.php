<?php
// Contact Form Handler for Synelogics Website
// This script processes contact form submissions with validation and email sending

// Set content type for JSON response
header('Content-Type: application/json');

// Enable CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Initialize response array
$response = [
    'success' => false,
    'message' => '',
    'errors' => []
];

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Invalid request method. Only POST requests are allowed.';
    echo json_encode($response);
    exit;
}

// Sanitize and validate input data
function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

function validatePhone($phone) {
    // Remove all non-numeric characters
    $phone = preg_replace('/[^0-9]/', '', $phone);
    // Check if phone number is between 10-15 digits
    return strlen($phone) >= 10 && strlen($phone) <= 15;
}

// Get and sanitize form data
$firstName = sanitizeInput($_POST['first_name'] ?? '');
$lastName = sanitizeInput($_POST['last_name'] ?? '');
$email = sanitizeInput($_POST['email'] ?? '');
$phone = sanitizeInput($_POST['phone'] ?? '');
$company = sanitizeInput($_POST['company'] ?? '');
$service = sanitizeInput($_POST['service'] ?? '');
$budget = sanitizeInput($_POST['budget'] ?? '');
$message = sanitizeInput($_POST['message'] ?? '');
$newsletter = isset($_POST['newsletter']) ? true : false;

// Validation
$errors = [];

// Required field validation
if (empty($firstName)) {
    $errors['first_name'] = 'First name is required.';
}

if (empty($lastName)) {
    $errors['last_name'] = 'Last name is required.';
}

if (empty($email)) {
    $errors['email'] = 'Email address is required.';
} elseif (!validateEmail($email)) {
    $errors['email'] = 'Please enter a valid email address.';
}

if (empty($message)) {
    $errors['message'] = 'Project details are required.';
} elseif (strlen($message) < 10) {
    $errors['message'] = 'Please provide more details about your project (minimum 10 characters).';
}

// Optional field validation
if (!empty($phone) && !validatePhone($phone)) {
    $errors['phone'] = 'Please enter a valid phone number.';
}

// Check for validation errors
if (!empty($errors)) {
    $response['errors'] = $errors;
    $response['message'] = 'Please correct the errors below and try again.';
    echo json_encode($response);
    exit;
}

// Prepare email content
$fullName = $firstName . ' ' . $lastName;
$currentDate = date('Y-m-d H:i:s');

// Email configuration
$to = '<EMAIL>'; // Change this to your actual email
$subject = 'New Contact Form Submission - Synelogics';

// Create email body
$emailBody = "
New Contact Form Submission

Date: {$currentDate}

Contact Information:
- Name: {$fullName}
- Email: {$email}
- Phone: " . (!empty($phone) ? $phone : 'Not provided') . "
- Company: " . (!empty($company) ? $company : 'Not provided') . "

Project Information:
- Service Interest: " . (!empty($service) ? ucfirst(str_replace('-', ' ', $service)) : 'Not specified') . "
- Budget Range: " . (!empty($budget) ? ucfirst(str_replace('-', ' ', $budget)) : 'Not specified') . "

Project Details:
{$message}

Newsletter Subscription: " . ($newsletter ? 'Yes' : 'No') . "

---
This message was sent from the Synelogics contact form.
";

// Email headers
$headers = [
    'From: <EMAIL>',
    'Reply-To: ' . $email,
    'X-Mailer: PHP/' . phpversion(),
    'Content-Type: text/plain; charset=UTF-8'
];

// Attempt to send email
$emailSent = false;

// Try to send email using PHP's mail() function
if (function_exists('mail')) {
    $emailSent = mail($to, $subject, $emailBody, implode("\r\n", $headers));
}

// If mail() function is not available or fails, log the submission
if (!$emailSent) {
    // Log the submission to a file (make sure the logs directory exists and is writable)
    $logDir = __DIR__ . '/logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logFile = $logDir . '/contact_submissions.log';
    $logEntry = "[{$currentDate}] Contact form submission from {$fullName} ({$email})\n";
    $logEntry .= "Service: {$service}, Budget: {$budget}\n";
    $logEntry .= "Message: {$message}\n";
    $logEntry .= "---\n\n";
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// Send auto-reply email to the user
$autoReplySubject = 'Thank you for contacting Synelogics';
$autoReplyBody = "
Dear {$firstName},

Thank you for reaching out to Synelogics! We have received your inquiry and appreciate your interest in our IT solutions.

Here's a summary of your submission:
- Service Interest: " . (!empty($service) ? ucfirst(str_replace('-', ' ', $service)) : 'General inquiry') . "
- Submitted on: {$currentDate}

Our team will review your requirements and get back to you within 24 hours. In the meantime, feel free to explore our services and case studies on our website.

If you have any urgent questions, please don't hesitate to call us at +****************.

Best regards,
The Synelogics Team

---
Synelogics - Innovative IT Solutions
Website: https://synelogics.com
Email: <EMAIL>
Phone: +****************
";

$autoReplyHeaders = [
    'From: Synelogics <<EMAIL>>',
    'X-Mailer: PHP/' . phpversion(),
    'Content-Type: text/plain; charset=UTF-8'
];

// Send auto-reply
if (function_exists('mail')) {
    mail($email, $autoReplySubject, $autoReplyBody, implode("\r\n", $autoReplyHeaders));
}

// Prepare success response
$response['success'] = true;
$response['message'] = 'Thank you for your message! We have received your inquiry and will get back to you within 24 hours.';

// Optional: Store submission in database
// You can uncomment and modify this section if you want to store submissions in a database
/*
try {
    $pdo = new PDO('mysql:host=localhost;dbname=synelogics', $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $pdo->prepare("
        INSERT INTO contact_submissions 
        (first_name, last_name, email, phone, company, service, budget, message, newsletter, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    
    $stmt->execute([
        $firstName, $lastName, $email, $phone, $company, 
        $service, $budget, $message, $newsletter ? 1 : 0
    ]);
    
} catch (PDOException $e) {
    // Log database error but don't fail the request
    error_log('Database error in contact form: ' . $e->getMessage());
}
*/

// Return JSON response
echo json_encode($response);

// Optional: Redirect for non-AJAX submissions
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
    // If this is not an AJAX request, redirect back to contact page with success message
    header('Location: /contact?success=1');
    exit;
}
?>
