<?php
/**
 * Debug Path Detection
 * Visit this page to see what paths are being generated
 */

// Include configuration
require_once __DIR__ . '/includes/config.php';

echo "<h1>Path Detection Debug</h1>";
echo "<pre>";

echo "=== SERVER VARIABLES ===\n";
echo "HTTP_HOST: " . ($_SERVER['HTTP_HOST'] ?? 'not set') . "\n";
echo "SCRIPT_NAME: " . ($_SERVER['SCRIPT_NAME'] ?? 'not set') . "\n";
echo "REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'not set') . "\n";
echo "DOCUMENT_ROOT: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'not set') . "\n";
echo "SCRIPT_FILENAME: " . ($_SERVER['SCRIPT_FILENAME'] ?? 'not set') . "\n";

echo "\n=== DEPLOYMENT CONFIG ===\n";
if (defined('FORCE_ROOT_DEPLOYMENT')) {
    echo "FORCE_ROOT_DEPLOYMENT: " . (FORCE_ROOT_DEPLOYMENT ? 'TRUE' : 'FALSE') . "\n";
} else {
    echo "FORCE_ROOT_DEPLOYMENT: NOT DEFINED\n";
}

echo "\n=== PATH DETECTION ===\n";
echo "getBasePath(): " . getBasePath() . "\n";
echo "getBaseUrl(): " . getBaseUrl() . "\n";

echo "\n=== SAMPLE URLS ===\n";
echo "url(): " . url() . "\n";
echo "url('about'): " . url('about') . "\n";
echo "url('services'): " . url('services') . "\n";
echo "url('services/software-development'): " . url('services/software-development') . "\n";
echo "asset('assets/css/style.css'): " . asset('assets/css/style.css') . "\n";

echo "\n=== DOMAIN CHECKS ===\n";
$host = $_SERVER['HTTP_HOST'] ?? '';
echo "Host contains 'synelogics.com': " . (strpos($host, 'synelogics.com') !== false ? 'YES' : 'NO') . "\n";
echo "Host contains 'synelogics.pk': " . (strpos($host, 'synelogics.pk') !== false ? 'YES' : 'NO') . "\n";

echo "\n=== FILE CHECKS ===\n";
$deploymentConfigPath = dirname(__DIR__) . '/deployment-config.php';
echo "Deployment config path: " . $deploymentConfigPath . "\n";
echo "Deployment config exists: " . (file_exists($deploymentConfigPath) ? 'YES' : 'NO') . "\n";

echo "</pre>";

echo "<h2>Test Links</h2>";
echo "<ul>";
echo "<li><a href='" . url() . "'>Home</a></li>";
echo "<li><a href='" . url('about') . "'>About</a></li>";
echo "<li><a href='" . url('services') . "'>Services</a></li>";
echo "<li><a href='" . url('services/software-development') . "'>Software Development</a></li>";
echo "<li><a href='" . url('sitemap') . "'>Sitemap</a></li>";
echo "</ul>";

echo "<p><strong>Instructions:</strong> Click the links above to test if they work correctly.</p>";
?>
