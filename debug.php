<?php
$page_title = "Debug Information | Synelogics";
include 'includes/header.php';
?>

<section class="section-padding bg-white">
    <div class="container-custom">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-4xl font-bold text-gray-900 mb-8">Debug Information</h1>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-blue-900 mb-4">Server Information</h2>
                    <ul class="space-y-2 text-blue-800">
                        <li><strong>Server Software:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></li>
                        <li><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></li>
                        <li><strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown'; ?></li>
                        <li><strong>Current Directory:</strong> <?php echo getcwd(); ?></li>
                    </ul>
                </div>
                
                <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-green-900 mb-4">Request Information</h2>
                    <ul class="space-y-2 text-green-800">
                        <li><strong>Request URI:</strong> <?php echo $_SERVER['REQUEST_URI'] ?? 'Unknown'; ?></li>
                        <li><strong>Script Name:</strong> <?php echo $_SERVER['SCRIPT_NAME'] ?? 'Unknown'; ?></li>
                        <li><strong>Query String:</strong> <?php echo $_SERVER['QUERY_STRING'] ?? 'None'; ?></li>
                        <li><strong>HTTP Host:</strong> <?php echo $_SERVER['HTTP_HOST'] ?? 'Unknown'; ?></li>
                    </ul>
                </div>
                
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-yellow-900 mb-4">File Existence Check</h2>
                    <?php
                    $files = [
                        'about.php',
                        'contact.php',
                        'services/index.php',
                        'services/software-development.php',
                        '.htaccess',
                        'router.php',
                        'simple-router.php'
                    ];
                    ?>
                    <ul class="space-y-2 text-yellow-800">
                        <?php foreach ($files as $file): ?>
                            <li>
                                <strong><?php echo $file; ?>:</strong> 
                                <span class="<?php echo file_exists($file) ? 'text-green-600' : 'text-red-600'; ?>">
                                    <?php echo file_exists($file) ? 'EXISTS' : 'MISSING'; ?>
                                </span>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-purple-900 mb-4">URL Tests</h2>
                    <ul class="space-y-2 text-purple-800">
                        <li><a href="/about" class="text-blue-600 hover:underline">Test /about (clean URL)</a></li>
                        <li><a href="/about.php" class="text-blue-600 hover:underline">Test /about.php (with extension)</a></li>
                        <li><a href="/services" class="text-blue-600 hover:underline">Test /services (clean URL)</a></li>
                        <li><a href="/services/software-development" class="text-blue-600 hover:underline">Test /services/software-development</a></li>
                        <li><a href="/nonexistent" class="text-blue-600 hover:underline">Test /nonexistent (should 404)</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="mt-8 bg-red-50 border border-red-200 rounded-lg p-6">
                <h2 class="text-xl font-semibold text-red-900 mb-4">Router Detection</h2>
                <p class="text-red-800">
                    <?php
                    if (isset($_SERVER['SCRIPT_NAME']) && strpos($_SERVER['SCRIPT_NAME'], 'router.php') !== false) {
                        echo "✅ Router is being used: " . $_SERVER['SCRIPT_NAME'];
                    } elseif (isset($_SERVER['SCRIPT_NAME']) && strpos($_SERVER['SCRIPT_NAME'], 'simple-router.php') !== false) {
                        echo "✅ Simple router is being used: " . $_SERVER['SCRIPT_NAME'];
                    } else {
                        echo "❌ No router detected. You're accessing files directly.";
                        echo "<br><strong>Current script:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'Unknown');
                        echo "<br><br><strong>To fix this:</strong>";
                        echo "<br>1. Stop your server (Ctrl+C)";
                        echo "<br>2. Start with: <code>php -S localhost:8000 simple-router.php</code>";
                    }
                    ?>
                </p>
            </div>
            
            <div class="mt-6 text-center">
                <a href="/" class="btn-primary">Return to Homepage</a>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
