<?php
/**
 * Deployment Configuration for Synelogics Website
 * 
 * This file helps you easily switch between development and production configurations.
 * 
 * INSTRUCTIONS FOR DEPLOYMENT:
 * 
 * 1. For PRODUCTION deployment (uploading directly to domain root):
 *    - Set FORCE_ROOT_DEPLOYMENT = true
 *    - Upload all files directly to your domain's public_html or htdocs folder
 *    - Your site will work at: http://synelogics.com.pk/
 * 
 * 2. For DEVELOPMENT (local testing in subdirectory):
 *    - Set FORCE_ROOT_DEPLOYMENT = false
 *    - Keep files in subdirectory like /synelogics/ or /synelogics-1/synelogics/
 *    - Your site will work at: http://localhost/synelogics/
 */

// Set this to TRUE when deploying to production domain root
// Set this to FALSE for local development in subdirectories
define('FORCE_ROOT_DEPLOYMENT', true);

// Debug: Show current path detection (remove in production)
if (isset($_GET['debug_paths'])) {
    echo "<pre>";
    echo "Current folder: " . basename(dirname($_SERVER['SCRIPT_FILENAME'])) . "\n";
    echo "Script path: " . $_SERVER['SCRIPT_NAME'] . "\n";
    echo "Document root: " . $_SERVER['DOCUMENT_ROOT'] . "\n";
    echo "Script filename: " . $_SERVER['SCRIPT_FILENAME'] . "\n";
    require_once 'includes/config.php';
    echo "Detected base path: " . getBasePath() . "\n";
    echo "Sample URL: " . url('about') . "\n";
    echo "Sample asset: " . asset('assets/css/style.css') . "\n";
    echo "</pre>";
    exit;
}

/**
 * Get the deployment-aware base path
 */
function getDeploymentBasePath() {
    // If forced to root deployment, always return root
    if (FORCE_ROOT_DEPLOYMENT) {
        return '/';
    }
    
    // Otherwise use the dynamic detection from config.php
    require_once __DIR__ . '/includes/config.php';
    return getBasePath();
}

/**
 * Get deployment-aware base URL
 */
function getDeploymentBaseUrl() {
    $basePath = getDeploymentBasePath();
    return rtrim($basePath, '/');
}

/**
 * Create deployment-aware URL
 */
function deploymentUrl($path = '') {
    $baseUrl = getDeploymentBaseUrl();
    $path = ltrim($path, '/');
    
    if (empty($path)) {
        return $baseUrl . '/';
    }
    
    return $baseUrl . '/' . $path;
}

/**
 * Create deployment-aware asset URL
 */
function deploymentAsset($assetPath) {
    return deploymentUrl($assetPath);
}

// Instructions for deployment
if (basename($_SERVER['PHP_SELF']) === 'deployment-config.php') {
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Synelogics Deployment Configuration</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; line-height: 1.6; }
            .config-box { background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .production { background: #e8f5e8; border-left: 4px solid #4caf50; }
            .development { background: #fff3cd; border-left: 4px solid #ffc107; }
            .current-config { background: #d1ecf1; border-left: 4px solid #17a2b8; }
            code { background: #f8f9fa; padding: 2px 6px; border-radius: 4px; font-family: monospace; }
            .step { margin: 15px 0; }
            .warning { background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 4px; color: #721c24; }
        </style>
    </head>
    <body>
        <h1>🚀 Synelogics Deployment Configuration</h1>
        
        <div class="current-config config-box">
            <h2>📊 Current Configuration</h2>
            <p><strong>FORCE_ROOT_DEPLOYMENT:</strong> <?php echo FORCE_ROOT_DEPLOYMENT ? 'TRUE (Production Mode)' : 'FALSE (Development Mode)'; ?></p>
            <p><strong>Current Base Path:</strong> <code><?php echo getDeploymentBasePath(); ?></code></p>
            <p><strong>Sample URL:</strong> <code><?php echo deploymentUrl('about'); ?></code></p>
        </div>

        <div class="production config-box">
            <h2>🌐 Production Deployment (Domain Root)</h2>
            <p>For deploying directly to <strong>http://synelogics.com.pk/</strong></p>
            
            <div class="step">
                <h3>Step 1: Update Configuration</h3>
                <p>Edit <code>deployment-config.php</code> and change:</p>
                <code>define('FORCE_ROOT_DEPLOYMENT', true);</code>
                <p><strong>Current setting:</strong> <?php echo FORCE_ROOT_DEPLOYMENT ? 'TRUE (Production Mode)' : 'FALSE (Development Mode)'; ?></p>
            </div>
            
            <div class="step">
                <h3>Step 2: Update .htaccess</h3>
                <p>Edit the <code>.htaccess</code> file and update the ErrorDocument path:</p>
                <ul>
                    <li>Comment out: <code>ErrorDocument 404 /synelogics-1/synelogics/error-handler.php</code></li>
                    <li>Uncomment: <code>ErrorDocument 404 /error-handler.php</code></li>
                </ul>
            </div>

            <div class="step">
                <h3>Step 3: Upload Files</h3>
                <p>Upload ALL files directly to your domain's root directory:</p>
                <ul>
                    <li>Upload to: <code>/public_html/</code> or <code>/htdocs/</code></li>
                    <li>NOT to: <code>/public_html/synelogics/</code></li>
                </ul>
            </div>
            
            <div class="step">
                <h3>Step 4: Test</h3>
                <p>Your site should work at:</p>
                <ul>
                    <li><code>http://synelogics.com.pk/</code></li>
                    <li><code>http://synelogics.com.pk/about</code></li>
                    <li><code>http://synelogics.com.pk/services</code></li>
                </ul>
                <p>Test 404 error handling by visiting a non-existent page like:</p>
                <ul>
                    <li><code>http://synelogics.com.pk/non-existent-page</code></li>
                </ul>
            </div>
        </div>

        <div class="development config-box">
            <h2>💻 Development Mode (Subdirectory)</h2>
            <p>For local testing in subdirectories like <strong>http://localhost/synelogics/</strong></p>
            
            <div class="step">
                <h3>Step 1: Keep Configuration</h3>
                <p>Keep <code>deployment-config.php</code> as:</p>
                <code>define('FORCE_ROOT_DEPLOYMENT', false);</code>
            </div>
            
            <div class="step">
                <h3>Step 2: File Location</h3>
                <p>Keep files in subdirectory:</p>
                <ul>
                    <li><code>/htdocs/synelogics/</code></li>
                    <li><code>/htdocs/synelogics-1/synelogics/</code></li>
                </ul>
            </div>
        </div>

        <div class="warning">
            <h3>⚠️ Important Notes</h3>
            <ul>
                <li>Always test your configuration before going live</li>
                <li>Make sure to update <code>FORCE_ROOT_DEPLOYMENT</code> before deployment</li>
                <li>Keep a backup of your files before deployment</li>
                <li>Test all major pages after deployment</li>
            </ul>
        </div>

        <h2>🔧 Quick Configuration Change</h2>
        <p>Current setting: <strong><?php echo FORCE_ROOT_DEPLOYMENT ? 'Production' : 'Development'; ?></strong></p>
        <p>To change, edit line 19 in <code>deployment-config.php</code></p>
    </body>
    </html>
    <?php
    exit;
}
?>
