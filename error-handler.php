<?php
/**
 * Dynamic Error Handler for Synelogics Website
 * 
 * This file handles 404 errors and redirects to the correct 404 page
 * regardless of whether the site is in a subdirectory or at domain root.
 */

// Set 404 status code
http_response_code(404);

// Include configuration for proper path handling
require_once __DIR__ . '/includes/config.php';

// Get the correct path to the 404 page
$error404Path = __DIR__ . '/404.php';

// Check if 404.php exists
if (file_exists($error404Path)) {
    // Include the 404 page
    include $error404Path;
} else {
    // Fallback error page if 404.php doesn't exist
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Page Not Found - Synelogics</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                margin: 0;
                padding: 0;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .error-container {
                text-align: center;
                color: white;
                max-width: 600px;
                padding: 2rem;
            }
            .error-code {
                font-size: 8rem;
                font-weight: bold;
                margin: 0;
                opacity: 0.8;
            }
            .error-title {
                font-size: 2rem;
                margin: 1rem 0;
            }
            .error-message {
                font-size: 1.1rem;
                margin: 1.5rem 0;
                opacity: 0.9;
            }
            .btn {
                display: inline-block;
                background: rgba(255, 255, 255, 0.2);
                color: white;
                padding: 12px 24px;
                text-decoration: none;
                border-radius: 8px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                transition: all 0.3s ease;
                margin: 0.5rem;
            }
            .btn:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: translateY(-2px);
            }
        </style>
    </head>
    <body>
        <div class="error-container">
            <h1 class="error-code">404</h1>
            <h2 class="error-title">Page Not Found</h2>
            <p class="error-message">
                The page you're looking for could not be found. It may have been moved, deleted, or you entered the wrong URL.
            </p>
            <div>
                <a href="<?php echo url(); ?>" class="btn">Return Home</a>
                <a href="<?php echo url('contact'); ?>" class="btn">Contact Us</a>
            </div>
        </div>
    </body>
    </html>
    <?php
}
?>
