<?php
$index = isset($_GET['index']) ? intval($_GET['index']) : 1;
$perPage = 5; // images per page
$apiKey = 'bHjY3gyS8bmXPpAIgdwS60jGaarPRa965aZbAQ8obZizoL5ngJVgRP8O'; // replace with your actual API key

$url = "https://api.pexels.com/v1/search?query=technology&per_page={$perPage}&page={$index}";

$ch = curl_init($url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    "Authorization: $apiKey"
]);
$response = curl_exec($ch);
curl_close($ch);

$data = json_decode($response, true);
$photos = $data['photos'] ?? [];

header("Content-Type: text/html");
?>
<!DOCTYPE html>
<html>
<head>
    <title>Indexed IT Images (Page <?= $index ?>)</title>
    <style>
        img { max-width: 300px; margin: 10px; border-radius: 10px; }
    </style>
</head>
<body>
    <h1>Stock Images (Index <?= $index ?>)</h1>
    <?php foreach ($photos as $photo): ?>
        <div>
            <img src="<?= htmlspecialchars($photo['src']['medium']) ?>" alt="Stock Image">
        </div>
    <?php endforeach; ?>
</body>
</html>
