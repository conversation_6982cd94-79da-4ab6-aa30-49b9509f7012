<?php
/**
 * Access Control System for Synelogics Website
 * This file should be included at the top of every page that needs protection
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Configuration
$ACCESS_ENABLED = true; // Set to false to disable access control
$SESSION_KEY = "synelogics_access_granted";
$ACCESS_PAGE = "access.php";

// List of pages that don't require access control
$PUBLIC_PAGES = [
    'access.php',
    'access-handler.php'
];

/**
 * Check if access control is needed
 */
function needsAccessControl() {
    global $PUBLIC_PAGES, $ACCESS_ENABLED;
    
    if (!$ACCESS_ENABLED) {
        return false;
    }
    
    $current_page = basename($_SERVER['PHP_SELF']);
    return !in_array($current_page, $PUBLIC_PAGES);
}

/**
 * Check if user has access
 */
function hasAccess() {
    global $SESSION_KEY;
    return isset($_SESSION[$SESSION_KEY]) && $_SESSION[$SESSION_KEY] === true;
}

/**
 * Redirect to access page
 */
function redirectToAccess() {
    global $ACCESS_PAGE;
    
    // Store the current URL for redirect after access
    $current_url = $_SERVER['REQUEST_URI'];
    $_SESSION['redirect_after_access'] = $current_url;
    
    // Redirect to access page
    $access_url = '/' . trim(dirname($_SERVER['SCRIPT_NAME']), '/') . '/' . $ACCESS_PAGE;
    $access_url = str_replace('//', '/', $access_url);
    
    header("Location: $access_url");
    exit;
}

/**
 * Main access control function
 */
function checkAccess() {
    if (needsAccessControl() && !hasAccess()) {
        redirectToAccess();
    }
}

// Auto-execute access control
checkAccess();

/**
 * Function to disable access control (for when removing the system)
 */
function disableAccessControl() {
    global $ACCESS_ENABLED;
    $ACCESS_ENABLED = false;
}

/**
 * Function to clear access session
 */
function clearAccess() {
    global $SESSION_KEY;
    unset($_SESSION[$SESSION_KEY]);
    unset($_SESSION['redirect_after_access']);
}
?>
