<?php
/**
 * Configuration file for base path handling
 * This ensures all paths work correctly regardless of subdirectory placement
 */

/**
 * Get the base path for the website
 * This function determines the correct base path whether running on:
 * - PHP built-in server (localhost:8000)
 * - Apache in root directory (localhost/)
 * - Apache in subdirectory (localhost/subfolder/)
 * - Production domain (example.com/)
 */
function getBasePath() {
    // Check if deployment config exists and use it
    $deploymentConfigPath = dirname(__DIR__) . '/deployment-config.php';
    if (file_exists($deploymentConfigPath)) {
        require_once $deploymentConfigPath;
        if (defined('FORCE_ROOT_DEPLOYMENT') && FORCE_ROOT_DEPLOYMENT) {
            return '/';
        }
    }

    // Get the current script's directory path
    $scriptPath = $_SERVER['SCRIPT_NAME'];
    $scriptDir = dirname($scriptPath);

    // Special handling for production site with redirect rule
    // If we're on the live site (synelogics.com.pk) and have a redirect rule
    if (isset($_SERVER['HTTP_HOST']) &&
        (strpos($_SERVER['HTTP_HOST'], 'synelogics.com') !== false ||
         strpos($_SERVER['HTTP_HOST'], 'synelogics.pk') !== false)) {

        // Always return /synelogics/ for the live site since files are in /synelogics/ folder
        // and there's a redirect rule from domain root to /synelogics/
        return '/synelogics/';
    }

    // If we're at the server root
    if ($scriptDir == '/' || $scriptDir == '\\') {
        return '/';
    }

    // Dynamic path detection - works with ANY folder name
    // Get the directory where the current script is located
    $currentScriptDir = dirname($_SERVER['SCRIPT_NAME']);

    // If the script is in the root directory, return root
    if ($currentScriptDir === '/' || $currentScriptDir === '\\') {
        return '/';
    }

    // Check if we're in a subdirectory by looking for index.php in the current directory
    $currentDir = dirname($_SERVER['SCRIPT_FILENAME']);
    if (file_exists($currentDir . '/index.php') || file_exists($currentDir . '/../index.php')) {
        // We're in the website directory or a subdirectory of it
        // Find the website root by looking for index.php
        $pathParts = explode('/', trim($currentScriptDir, '/'));

        // Start from the current directory and work backwards to find the website root
        for ($i = count($pathParts); $i >= 0; $i--) {
            $testPath = '/' . implode('/', array_slice($pathParts, 0, $i));
            if ($testPath === '') $testPath = '/';

            $testDir = $_SERVER['DOCUMENT_ROOT'] . $testPath;
            if (file_exists($testDir . '/index.php') && file_exists($testDir . '/includes/config.php')) {
                return $testPath === '/' ? '/' : $testPath . '/';
            }
        }
    }

    // Fallback: return the directory of the current script
    return $currentScriptDir === '/' ? '/' : $currentScriptDir . '/';
}

/**
 * Get the base URL for assets and links
 * Returns the base path without trailing slash for use in URLs
 */
function getBaseUrl() {
    $basePath = getBasePath();
    return rtrim($basePath, '/');
}

/**
 * Create a URL relative to the website root
 * @param string $path The path relative to the website root (e.g., 'about', 'services/software-development')
 * @return string The complete URL
 */
function url($path = '') {
    $baseUrl = getBaseUrl();
    $path = ltrim($path, '/');
    
    if (empty($path)) {
        return $baseUrl . '/';
    }
    
    return $baseUrl . '/' . $path;
}

/**
 * Create an asset URL (for CSS, JS, images)
 * @param string $assetPath The path to the asset (e.g., 'assets/css/style.css')
 * @return string The complete asset URL
 */
function asset($assetPath) {
    return url($assetPath);
}

// Set global base path variables for backward compatibility
$GLOBALS['BASE_PATH'] = getBasePath();
$GLOBALS['BASE_URL'] = getBaseUrl();
?>
