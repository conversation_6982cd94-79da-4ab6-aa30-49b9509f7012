<?php
/**
 * Configuration file for base path handling
 * This ensures all paths work correctly regardless of subdirectory placement
 */

/**
 * Get the base path for the website
 * This function determines the correct base path whether running on:
 * - PHP built-in server (localhost:8000)
 * - Apache in root directory (localhost/synelogics/)
 * - Apache in subdirectory (localhost/subfolder/synelogics/)
 * - Apache with document root pointing directly to synelogics folder (localhost/)
 */
function getBasePath() {
    // Get the current script's directory path
    $scriptPath = $_SERVER['SCRIPT_NAME'];

    // Check if we're running with document root pointing directly to synelogics
    // In this case, the script path won't contain 'synelogics'
    if (strpos($scriptPath, '/synelogics/') === false) {
        // Document root is pointing directly to synelogics folder
        return '/';
    }

    // Find the position of 'synelogics' in the path
    $synelogicsPos = strpos($scriptPath, '/synelogics/');

    if ($synelogicsPos !== false) {
        // Extract everything up to and including 'synelogics/'
        $basePath = substr($scriptPath, 0, $synelogicsPos + strlen('/synelogics/'));
    } else {
        // Fallback: assume we're in the root
        $basePath = '/';
    }

    return $basePath;
}

/**
 * Get the base URL for assets and links
 * Returns the base path without trailing slash for use in URLs
 */
function getBaseUrl() {
    $basePath = getBasePath();
    return rtrim($basePath, '/');
}

/**
 * Create a URL relative to the website root
 * @param string $path The path relative to the website root (e.g., 'about', 'services/software-development')
 * @return string The complete URL
 */
function url($path = '') {
    $baseUrl = getBaseUrl();
    $path = ltrim($path, '/');
    
    if (empty($path)) {
        return $baseUrl . '/';
    }
    
    return $baseUrl . '/' . $path;
}

/**
 * Create an asset URL (for CSS, JS, images)
 * @param string $assetPath The path to the asset (e.g., 'assets/css/style.css')
 * @return string The complete asset URL
 */
function asset($assetPath) {
    return url($assetPath);
}

// Set global base path variables for backward compatibility
$GLOBALS['BASE_PATH'] = getBasePath();
$GLOBALS['BASE_URL'] = getBaseUrl();
?>
