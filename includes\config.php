<?php
/**
 * Configuration file for base path handling
 * This ensures all paths work correctly regardless of subdirectory placement
 */

/**
 * Get the base path for the website
 * This function determines the correct base path whether running on:
 * - PHP built-in server (localhost:8000)
 * - Apache in root directory (localhost/)
 * - Apache in subdirectory (localhost/subfolder/)
 * - Production domain (example.com/)
 */
function getBasePath() {
    // Check if deployment config exists and use it
    $deploymentConfigPath = dirname(__DIR__) . '/deployment-config.php';
    if (file_exists($deploymentConfigPath)) {
        require_once $deploymentConfigPath;
        if (defined('FORCE_ROOT_DEPLOYMENT') && FORCE_ROOT_DEPLOYMENT) {
            return '/';
        }
    }

    // Get the current script's directory path
    $scriptPath = $_SERVER['SCRIPT_NAME'];
    $scriptDir = dirname($scriptPath);

    // Special handling for production site with redirect rule
    // If we're on the live site (synelogics.com.pk) and have a redirect rule
    if (isset($_SERVER['HTTP_HOST']) &&
        (strpos($_SERVER['HTTP_HOST'], 'synelogics.com') !== false ||
         strpos($_SERVER['HTTP_HOST'], 'synelogics.pk') !== false)) {

        // Always return /synelogics/ for the live site since files are in /synelogics/ folder
        // and there's a redirect rule from domain root to /synelogics/
        return '/synelogics/';
    }

    // If we're at the server root
    if ($scriptDir == '/' || $scriptDir == '\\') {
        return '/';
    }

    // Get the document root and the directory of the current script
    $docRoot = $_SERVER['DOCUMENT_ROOT'];
    $scriptRealPath = realpath(dirname($_SERVER['SCRIPT_FILENAME']));

    // If the script is directly in the document root, we're at the root
    if (strpos($scriptRealPath, $docRoot) === 0 && strlen($scriptRealPath) == strlen($docRoot)) {
        return '/';
    }

    // Check if we're running with document root pointing directly to site folder
    if (strpos($scriptPath, '/synelogics/') === false &&
        strpos($scriptPath, '/synelogics-1/') === false) {
        // Document root is pointing directly to site folder
        return '/';
    }

    // Find the position of site folder in the path
    $sitePos = false;

    // Check for nested synelogics structure (production)
    if (strpos($scriptPath, '/synelogics/synelogics/') !== false) {
        $sitePos = strpos($scriptPath, '/synelogics/synelogics/');
        $siteName = '/synelogics/synelogics/';
    }
    // Check for single synelogics folder (production alternative)
    elseif (strpos($scriptPath, '/synelogics/') !== false) {
        $sitePos = strpos($scriptPath, '/synelogics/');
        $siteName = '/synelogics/';
    }
    // Check for development environment
    elseif (strpos($scriptPath, '/synelogics-1/') !== false) {
        $sitePos = strpos($scriptPath, '/synelogics-1/');
        $siteName = '/synelogics-1/';
    }

    if ($sitePos !== false) {
        // Extract everything up to and including site folder
        $basePath = substr($scriptPath, 0, $sitePos + strlen($siteName));
    } else {
        // Fallback: assume we're in the root
        $basePath = '/';
    }

    return $basePath;
}

/**
 * Get the base URL for assets and links
 * Returns the base path without trailing slash for use in URLs
 */
function getBaseUrl() {
    $basePath = getBasePath();
    return rtrim($basePath, '/');
}

/**
 * Create a URL relative to the website root
 * @param string $path The path relative to the website root (e.g., 'about', 'services/software-development')
 * @return string The complete URL
 */
function url($path = '') {
    $baseUrl = getBaseUrl();
    $path = ltrim($path, '/');
    
    if (empty($path)) {
        return $baseUrl . '/';
    }
    
    return $baseUrl . '/' . $path;
}

/**
 * Create an asset URL (for CSS, JS, images)
 * @param string $assetPath The path to the asset (e.g., 'assets/css/style.css')
 * @return string The complete asset URL
 */
function asset($assetPath) {
    return url($assetPath);
}

// Set global base path variables for backward compatibility
$GLOBALS['BASE_PATH'] = getBasePath();
$GLOBALS['BASE_URL'] = getBaseUrl();
?>
