<?php
/**
 * Optimized Consultation Form Component
 * Clean, streamlined consultation form for service pages
 */

// Include database connection
require_once __DIR__ . '/database.php';

// Generate unique form ID for multiple forms on same page
$form_id = 'consultation-form-' . uniqid();
?>

<!-- Optimized Consultation Form Section -->
<section id="consultation" class="section-padding bg-gradient-to-r from-primary-600 to-secondary-600">
    <div class="container-custom">
        <div class="max-w-6xl mx-auto">
            <div class="bg-white rounded-2xl shadow-2xl overflow-hidden">
                <div class="grid grid-cols-1 lg:grid-cols-5">
                    <!-- Form Content -->
                    <div class="lg:col-span-3 p-6 lg:p-8">
                        <div class="mb-6">
                            <h2 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-3">Get Your Free Consultation</h2>
                            <p class="text-gray-600 flex items-center gap-4">
                                <span class="flex items-center gap-1">
                                    <i class="fas fa-clock text-primary-600"></i>
                                    2 min form
                                </span>
                                <span class="flex items-center gap-1">
                                    <i class="fas fa-gift text-primary-600"></i>
                                    Free consultation
                                </span>
                                <span class="flex items-center gap-1">
                                    <i class="fas fa-user-tie text-primary-600"></i>
                                    Expert guidance
                                </span>
                            </p>
                        </div>
                        
                        <form id="<?php echo $form_id; ?>" class="consultation-form space-y-6" method="POST" action="<?php echo url('consultation-handler'); ?>">
                            <!-- Required Fields Section -->
                            <div class="form-section required">
                                <h3>
                                    <i class="fas fa-asterisk text-red-500 text-sm"></i>
                                    Required Information
                                </h3>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="form-label-optimized" for="<?php echo $form_id; ?>_name">Full Name</label>
                                        <input type="text"
                                               id="<?php echo $form_id; ?>_name"
                                               name="name"
                                               class="form-input-optimized"
                                               placeholder="Your full name"
                                               required>
                                    </div>
                                    <div>
                                        <label class="form-label-optimized" for="<?php echo $form_id; ?>_email">Email Address</label>
                                        <input type="email"
                                               id="<?php echo $form_id; ?>_email"
                                               name="email"
                                               class="form-input-optimized"
                                               placeholder="<EMAIL>"
                                               required>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="form-label-optimized" for="<?php echo $form_id; ?>_phone">Phone Number</label>
                                        <input type="tel"
                                               id="<?php echo $form_id; ?>_phone"
                                               name="phone"
                                               class="form-input-optimized"
                                               placeholder="+****************"
                                               required>
                                    </div>
                                    <div>
                                        <label class="form-label-optimized" for="<?php echo $form_id; ?>_location">Location</label>
                                        <input type="text"
                                               id="<?php echo $form_id; ?>_location"
                                               name="location"
                                               class="form-input-optimized"
                                               placeholder="City, State/Country"
                                               required>
                                    </div>
                                </div>

                                <div>
                                    <label class="form-label-optimized" for="<?php echo $form_id; ?>_service">Service Needed</label>
                                    <select id="<?php echo $form_id; ?>_service"
                                            name="preferred_service"
                                            class="form-input-optimized"
                                            required>
                                        <option value="">Choose your service...</option>

                                        <!-- Software Development -->
                                        <optgroup label="Software Development">
                                            <option value="web-mobile-development">Web & Mobile Development</option>
                                            <option value="erp-crm-development">ERP/CRM Development</option>
                                            <option value="saas-development">SaaS Development</option>
                                        </optgroup>

                                        <!-- Cloud & DevOps -->
                                        <optgroup label="Cloud & DevOps">
                                            <option value="cloud-migrations">Cloud Migrations</option>
                                            <option value="infrastructure-automation">Infrastructure Automation</option>
                                            <option value="cicd-pipeline">CI/CD Pipeline</option>
                                        </optgroup>

                                        <!-- Data Analytics -->
                                        <optgroup label="Data Analytics">
                                            <option value="data-warehousing">Data Warehousing</option>
                                            <option value="bi-dashboards">BI Dashboards</option>
                                            <option value="etl-services">ETL Services</option>
                                            <option value="predictive-analytics">Predictive Analytics</option>
                                        </optgroup>

                                        <!-- Cybersecurity -->
                                        <optgroup label="Cybersecurity">
                                            <option value="managed-security">Managed Security</option>
                                            <option value="penetration-testing">Penetration Testing</option>
                                            <option value="compliance-consulting">Compliance Consulting</option>
                                        </optgroup>

                                        <!-- Managed IT -->
                                        <optgroup label="Managed IT">
                                            <option value="helpdesk-outsourcing">Helpdesk & Outsourcing</option>
                                            <option value="infrastructure-management">Infrastructure Management</option>
                                            <option value="it-strategy-planning">IT Strategy & Planning</option>
                                        </optgroup>

                                        <!-- Technology Consultancy -->
                                        <optgroup label="Technology Consultancy">
                                            <option value="other">Other</option>
                                        </optgroup>
                                    </select>
                                </div>
                            </div>

                            <!-- Optional Fields Section -->
                            <div class="form-section optional">
                                <h3>
                                    <i class="fas fa-plus-circle text-blue-600"></i>
                                    Additional Details <span class="text-sm font-normal text-gray-500">(Optional)</span>
                                </h3>

                                <div class="space-y-4">
                                    <div>
                                        <label class="form-label-optimized" for="<?php echo $form_id; ?>_organization">Organization/Company</label>
                                        <input type="text"
                                               id="<?php echo $form_id; ?>_organization"
                                               name="organization"
                                               class="form-input-optimized"
                                               placeholder="Your company name (optional)">
                                    </div>

                                    <div>
                                        <label class="form-label-optimized" for="<?php echo $form_id; ?>_description">Project Details & Comments</label>
                                        <textarea id="<?php echo $form_id; ?>_description"
                                                  name="description"
                                                  rows="4"
                                                  class="form-input-optimized"
                                                  placeholder="Brief description of your project, timeline, budget range, or specific requirements... (optional)"></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- Consent & Submit -->
                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <input type="checkbox"
                                           id="<?php echo $form_id; ?>_consent"
                                           name="consent"
                                           class="mt-1 mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                           required>
                                    <label for="<?php echo $form_id; ?>_consent" class="text-sm text-gray-700">
                                        I agree to be contacted regarding this consultation and understand my information is protected by our
                                        <a href="<?php echo url('privacy-policy'); ?>" class="text-blue-600 hover:text-blue-700 underline">Privacy Policy</a>.
                                    </label>
                                </div>

                                <button type="submit" class="btn-primary-optimized">
                                    <i class="fas fa-calendar-check"></i>
                                    Schedule Free Consultation
                                </button>

                                <p class="text-xs text-gray-500 text-center">
                                    <i class="fas fa-clock mr-1"></i>
                                    Response within 24 hours • No commitment required
                                </p>
                            </div>
                        </form>
                        
                        <!-- Success/Error Messages -->
                        <div id="<?php echo $form_id; ?>_message" class="mt-6 hidden">
                            <div class="success-message hidden p-4 bg-green-50 border border-green-200 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle text-green-600 mr-3"></i>
                                    <div>
                                        <h4 class="text-green-800 font-semibold">Consultation Request Submitted!</h4>
                                        <p class="text-green-700 text-sm mt-1">We'll contact you within 24 hours to schedule your free consultation.</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="error-message hidden p-4 bg-red-50 border border-red-200 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-exclamation-circle text-red-600 mr-3"></i>
                                    <div>
                                        <h4 class="text-red-800 font-semibold">Submission Failed</h4>
                                        <p class="text-red-700 text-sm mt-1">Please try again or contact us directly.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Benefits Panel -->
                    <div class="lg:col-span-2 bg-gradient-to-br from-primary-50 to-secondary-50 p-6 lg:p-8">
                        <div class="space-y-6">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-gradient-to-r from-primary-600 to-secondary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-handshake text-2xl text-white"></i>
                                </div>
                                <h3 class="text-xl font-bold text-gray-900 mb-2">Why Choose Our Consultation?</h3>
                                <p class="text-gray-600 text-sm">Get expert guidance tailored to your specific needs</p>
                            </div>

                            <div class="space-y-4">
                                <div class="flex items-start gap-3">
                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-check text-green-600 text-sm"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-900 text-sm">100% Free Consultation</h4>
                                        <p class="text-gray-600 text-xs">No hidden fees or commitments</p>
                                    </div>
                                </div>

                                <div class="flex items-start gap-3">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-clock text-blue-600 text-sm"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-900 text-sm">Quick Response</h4>
                                        <p class="text-gray-600 text-xs">We'll contact you within 24 hours</p>
                                    </div>
                                </div>

                                <div class="flex items-start gap-3">
                                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-user-tie text-purple-600 text-sm"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-900 text-sm">Expert Guidance</h4>
                                        <p class="text-gray-600 text-xs">Speak directly with our technical experts</p>
                                    </div>
                                </div>

                                <div class="flex items-start gap-3">
                                    <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-lightbulb text-orange-600 text-sm"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-900 text-sm">Custom Solutions</h4>
                                        <p class="text-gray-600 text-xs">Tailored recommendations for your business</p>
                                    </div>
                                </div>

                                <div class="flex items-start gap-3">
                                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-shield-alt text-red-600 text-sm"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-900 text-sm">Confidential & Secure</h4>
                                        <p class="text-gray-600 text-xs">Your information is completely protected</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-lg p-4 border border-primary-200">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-primary-600">500+</div>
                                    <div class="text-sm text-gray-600">Successful Projects</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Professional Form Styles -->
<style>
.form-label-optimized {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.form-input-optimized {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    background-color: #ffffff;
    font-size: 0.875rem;
    transition: all 0.2s ease-in-out;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.form-input-optimized:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input-optimized.valid {
    border-color: #10b981;
    background-color: #f0fdf4;
}

.form-input-optimized.error {
    border-color: #ef4444;
    background-color: #fef2f2;
}

.btn-primary-optimized {
    width: 100%;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-primary-optimized:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
    transform: translateY(-1px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.btn-primary-optimized:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Section styling */
.form-section {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-section.required {
    border-left: 4px solid #ef4444;
}

.form-section.optional {
    border-left: 4px solid #3b82f6;
}

.form-section h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .form-input-optimized {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 0.875rem 1rem;
    }

    .btn-primary-optimized {
        padding: 1rem 1.5rem;
        font-size: 1.125rem;
    }

    .form-section {
        padding: 1rem;
    }
}
</style>



<script>
// Optimized form handling for consultation form
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('<?php echo $form_id; ?>');
    const messageContainer = document.getElementById('<?php echo $form_id; ?>_message');
    const successMessage = messageContainer?.querySelector('.success-message');
    const errorMessage = messageContainer?.querySelector('.error-message');

    // Add form validation and enhancement
    const requiredFields = form.querySelectorAll('input[required], select[required]');

    // Real-time validation
    requiredFields.forEach(field => {
        field.addEventListener('blur', function() {
            validateField(this);
        });

        field.addEventListener('input', function() {
            if (this.classList.contains('error')) {
                validateField(this);
            }
        });
    });

    function validateField(field) {
        const isValid = field.checkValidity();

        if (isValid) {
            field.classList.remove('error');
            field.classList.add('valid');
            field.style.borderColor = '#10b981';
        } else {
            field.classList.remove('valid');
            field.classList.add('error');
            field.style.borderColor = '#ef4444';
        }

        return isValid;
    }

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Validate all required fields
        let isFormValid = true;
        requiredFields.forEach(field => {
            if (!validateField(field)) {
                isFormValid = false;
            }
        });

        if (!isFormValid) {
            // Scroll to first error field
            const firstError = form.querySelector('.error');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstError.focus();
            }
            return;
        }

        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending Request...';
        submitBtn.disabled = true;
        submitBtn.style.opacity = '0.7';

        // Hide previous messages
        if (messageContainer) {
            messageContainer.classList.add('hidden');
            successMessage?.classList.add('hidden');
            errorMessage?.classList.add('hidden');
        }

        // Prepare form data
        const formData = new FormData(form);
        
        // Submit form via AJAX
        fetch('/synelogics/consultation-handler.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (messageContainer) {
                messageContainer.classList.remove('hidden');
            }

            if (data.success) {
                if (successMessage) {
                    successMessage.classList.remove('hidden');
                }

                // Reset form with animation
                form.reset();
                requiredFields.forEach(field => {
                    field.classList.remove('valid', 'error');
                    field.style.borderColor = '';
                });

                // Show success animation
                submitBtn.innerHTML = '<i class="fas fa-check mr-2"></i>Request Sent Successfully!';
                submitBtn.style.background = 'linear-gradient(to right, #10b981, #059669)';

                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.style.background = '';
                    submitBtn.disabled = false;
                    submitBtn.style.opacity = '';
                }, 3000);

                // Track conversion (if analytics available)
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'consultation_request', {
                        'event_category': 'form_submission',
                        'event_label': 'consultation_form_optimized'
                    });
                }
            } else {
                if (errorMessage) {
                    errorMessage.classList.remove('hidden');
                    if (data.message) {
                        errorMessage.querySelector('p').textContent = data.message;
                    }
                }

                // Reset button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                submitBtn.style.opacity = '';
            }
        })
                    errorMessage.querySelector('p').textContent = data.message;
                }
            }
        })
        .catch(error => {
            console.error('Form submission error:', error);
            messageContainer.classList.remove('hidden');
            errorMessage.classList.remove('hidden');
        })
        .finally(() => {
            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });
});
</script>
