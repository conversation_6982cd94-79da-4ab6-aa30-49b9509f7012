<?php
/**
 * Database Configuration and Initialization for Synelogics Website
 * SQLite database for storing consultation form submissions
 */

class Database {
    private $db;
    private $dbPath;
    
    public function __construct() {
        $this->dbPath = __DIR__ . '/../data/synelogics.db';
        $this->initializeDatabase();
    }
    
    private function initializeDatabase() {
        try {
            // Create data directory if it doesn't exist
            $dataDir = dirname($this->dbPath);
            if (!is_dir($dataDir)) {
                mkdir($dataDir, 0755, true);
            }
            
            // Create or connect to SQLite database
            $this->db = new PDO('sqlite:' . $this->dbPath);
            $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Create tables if they don't exist
            $this->createTables();
            
        } catch (PDOException $e) {
            error_log('Database initialization error: ' . $e->getMessage());
            throw new Exception('Database connection failed');
        }
    }
    
    private function createTables() {
        // Consultation submissions table
        $consultationTable = "
            CREATE TABLE IF NOT EXISTS consultation_submissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(255) NOT NULL,
                email VARCHAR(255) NOT NULL,
                organization VARCHAR(255),
                phone VARCHAR(50),
                location VARCHAR(255),
                preferred_service VARCHAR(255),
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                status VARCHAR(50) DEFAULT 'new',
                follow_up_date DATETIME,
                notes TEXT
            )
        ";
        
        // Contact form submissions table (existing)
        $contactTable = "
            CREATE TABLE IF NOT EXISTS contact_submissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                first_name VARCHAR(255) NOT NULL,
                last_name VARCHAR(255) NOT NULL,
                email VARCHAR(255) NOT NULL,
                phone VARCHAR(50),
                company VARCHAR(255),
                service VARCHAR(255),
                budget VARCHAR(255),
                message TEXT NOT NULL,
                newsletter BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                status VARCHAR(50) DEFAULT 'new'
            )
        ";
        
        // Services lookup table
        $servicesTable = "
            CREATE TABLE IF NOT EXISTS services (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                category VARCHAR(255) NOT NULL,
                service_name VARCHAR(255) NOT NULL,
                service_slug VARCHAR(255) NOT NULL,
                description TEXT,
                active BOOLEAN DEFAULT 1,
                sort_order INTEGER DEFAULT 0
            )
        ";
        
        try {
            $this->db->exec($consultationTable);
            $this->db->exec($contactTable);
            $this->db->exec($servicesTable);
            
            // Insert default services if table is empty
            $this->insertDefaultServices();
            
        } catch (PDOException $e) {
            error_log('Table creation error: ' . $e->getMessage());
            throw new Exception('Failed to create database tables');
        }
    }
    
    private function insertDefaultServices() {
        // Check if services already exist
        $stmt = $this->db->query("SELECT COUNT(*) FROM services");
        $count = $stmt->fetchColumn();
        
        if ($count == 0) {
            $services = [
                // Software Development
                ['Software Development', 'Web & Mobile App Development', 'web-mobile-development', 'Custom web and mobile applications'],
                ['Software Development', 'Custom ERP/CRM Solutions', 'erp-crm-development', 'Enterprise resource planning and customer relationship management'],
                ['Software Development', 'SaaS Product Development', 'saas-development', 'Software-as-a-Service platform development'],
                
                // Cloud & DevOps
                ['Cloud & DevOps', 'Cloud Migrations', 'cloud-migration', 'AWS, Azure, and GCP cloud migration services'],
                ['Cloud & DevOps', 'Infrastructure Automation', 'infrastructure-automation', 'Terraform, Ansible, and infrastructure as code'],
                ['Cloud & DevOps', 'CI/CD Pipeline Setup', 'cicd-pipeline', 'Continuous integration and deployment pipelines'],
                
                // Data Analytics & BI
                ['Data Analytics & BI', 'Data Warehousing', 'data-warehousing', 'Snowflake, Redshift, and data warehouse solutions'],
                ['Data Analytics & BI', 'BI Dashboards', 'bi-dashboards', 'Power BI, Tableau, and business intelligence dashboards'],
                ['Data Analytics & BI', 'ETL/ELT Services', 'etl-services', 'Data extraction, transformation, and loading'],
                ['Data Analytics & BI', 'Predictive Analytics & AI', 'predictive-analytics', 'Machine learning and AI-powered insights'],
                
                // Cybersecurity
                ['Cybersecurity', 'Managed Security Services', 'managed-security', '24/7 security monitoring and threat response'],
                ['Cybersecurity', 'Penetration Testing', 'penetration-testing', 'Security assessments and vulnerability testing'],
                ['Cybersecurity', 'Compliance Consulting', 'compliance-consulting', 'GDPR, HIPAA, ISO compliance consulting'],
                
                // Managed IT Services
                ['Managed IT Services', 'IT Helpdesk Outsourcing', 'helpdesk-outsourcing', '24/7 IT support and helpdesk services'],
                ['Managed IT Services', 'Remote Infrastructure Management', 'infrastructure-management', 'Proactive infrastructure monitoring and management'],
                ['Managed IT Services', 'IT Audits and Strategy', 'it-strategy', 'IT audits, strategy, and consulting services'],
                
                // General
                ['General', 'Technology Consulting', 'technology-consulting', 'Strategic technology consulting and advisory'],
                ['General', 'Digital Transformation', 'digital-transformation', 'Digital transformation strategy and implementation'],
                ['General', 'Other', 'other', 'Other IT services not listed above']
            ];
            
            $stmt = $this->db->prepare("
                INSERT INTO services (category, service_name, service_slug, description, sort_order) 
                VALUES (?, ?, ?, ?, ?)
            ");
            
            foreach ($services as $index => $service) {
                $stmt->execute([
                    $service[0], $service[1], $service[2], $service[3], $index
                ]);
            }
        }
    }
    
    public function getConnection() {
        return $this->db;
    }
    
    public function getAllServices() {
        try {
            $stmt = $this->db->query("
                SELECT category, service_name, service_slug, description 
                FROM services 
                WHERE active = 1 
                ORDER BY sort_order, service_name
            ");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log('Error fetching services: ' . $e->getMessage());
            return [];
        }
    }
    
    public function getServicesByCategory() {
        try {
            $stmt = $this->db->query("
                SELECT category, service_name, service_slug, description 
                FROM services 
                WHERE active = 1 
                ORDER BY category, sort_order, service_name
            ");
            $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Group by category
            $grouped = [];
            foreach ($services as $service) {
                $grouped[$service['category']][] = $service;
            }
            
            return $grouped;
        } catch (PDOException $e) {
            error_log('Error fetching services by category: ' . $e->getMessage());
            return [];
        }
    }
    
    public function insertConsultationSubmission($data) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO consultation_submissions 
                (name, email, organization, phone, location, preferred_service, description) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $data['name'],
                $data['email'],
                $data['organization'] ?? null,
                $data['phone'] ?? null,
                $data['location'] ?? null,
                $data['preferred_service'] ?? null,
                $data['description'] ?? null
            ]);
            
            return $this->db->lastInsertId();
        } catch (PDOException $e) {
            error_log('Error inserting consultation submission: ' . $e->getMessage());
            throw new Exception('Failed to save consultation request');
        }
    }
    
    public function insertContactSubmission($data) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO contact_submissions 
                (first_name, last_name, email, phone, company, service, budget, message, newsletter) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $data['first_name'],
                $data['last_name'],
                $data['email'],
                $data['phone'] ?? null,
                $data['company'] ?? null,
                $data['service'] ?? null,
                $data['budget'] ?? null,
                $data['message'],
                $data['newsletter'] ? 1 : 0
            ]);
            
            return $this->db->lastInsertId();
        } catch (PDOException $e) {
            error_log('Error inserting contact submission: ' . $e->getMessage());
            throw new Exception('Failed to save contact request');
        }
    }
    
    public function getRecentSubmissions($limit = 10) {
        try {
            $stmt = $this->db->prepare("
                SELECT 'consultation' as type, name, email, organization, created_at 
                FROM consultation_submissions 
                UNION ALL 
                SELECT 'contact' as type, 
                       CONCAT(first_name, ' ', last_name) as name, 
                       email, company as organization, created_at 
                FROM contact_submissions 
                ORDER BY created_at DESC 
                LIMIT ?
            ");
            
            $stmt->execute([$limit]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log('Error fetching recent submissions: ' . $e->getMessage());
            return [];
        }
    }
}

// Initialize database connection
try {
    $database = new Database();
} catch (Exception $e) {
    error_log('Database initialization failed: ' . $e->getMessage());
    // In production, you might want to show a user-friendly error page
}
?>
