<?php
/**
 * Email Configuration for Synelogics Website
 * SMTP configuration and email sending functions
 */

// Email configuration constants
define('SMTP_HOST', 'smtp.gmail.com'); // Change to your SMTP server
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>'); // Change to your email
define('SMTP_PASSWORD', 'your-app-password'); // Change to your app password
define('SMTP_ENCRYPTION', 'tls');

define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Synelogics');
define('SALES_EMAIL', '<EMAIL>'); // Change to your sales email
define('ADMIN_EMAIL', '<EMAIL>'); // Change to your admin email

/**
 * Send email using <PERSON><PERSON>'s mail() function or SMTP
 * This is a basic implementation - for production, consider using PHPMailer or similar
 */
function sendEmail($to, $subject, $body, $isHtml = false) {
    try {
        // For basic implementation using PHP mail()
        $headers = [
            'From: ' . FROM_NAME . ' <' . FROM_EMAIL . '>',
            'Reply-To: ' . FROM_EMAIL,
            'X-Mailer: PHP/' . phpversion()
        ];
        
        if ($isHtml) {
            $headers[] = 'Content-Type: text/html; charset=UTF-8';
        } else {
            $headers[] = 'Content-Type: text/plain; charset=UTF-8';
        }
        
        $headerString = implode("\r\n", $headers);
        
        // Attempt to send email
        $sent = mail($to, $subject, $body, $headerString);
        
        if ($sent) {
            error_log("Email sent successfully to: {$to}");
            return true;
        } else {
            error_log("Failed to send email to: {$to}");
            return false;
        }
        
    } catch (Exception $e) {
        error_log('Email sending error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Send email using SMTP (requires PHPMailer or similar library)
 * Uncomment and configure this function if you want to use SMTP
 */
/*
function sendEmailSMTP($to, $subject, $body, $isHtml = false) {
    // This function requires PHPMailer library
    // composer require phpmailer/phpmailer
    
    use PHPMailer\PHPMailer\PHPMailer;
    use PHPMailer\PHPMailer\SMTP;
    use PHPMailer\PHPMailer\Exception;
    
    require_once __DIR__ . '/../vendor/autoload.php';
    
    $mail = new PHPMailer(true);
    
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host       = SMTP_HOST;
        $mail->SMTPAuth   = true;
        $mail->Username   = SMTP_USERNAME;
        $mail->Password   = SMTP_PASSWORD;
        $mail->SMTPSecure = SMTP_ENCRYPTION;
        $mail->Port       = SMTP_PORT;
        
        // Recipients
        $mail->setFrom(FROM_EMAIL, FROM_NAME);
        $mail->addAddress($to);
        $mail->addReplyTo(FROM_EMAIL, FROM_NAME);
        
        // Content
        $mail->isHTML($isHtml);
        $mail->Subject = $subject;
        $mail->Body    = $body;
        
        $mail->send();
        error_log("SMTP email sent successfully to: {$to}");
        return true;
        
    } catch (Exception $e) {
        error_log("SMTP email failed to: {$to}. Error: {$mail->ErrorInfo}");
        return false;
    }
}
*/

/**
 * Get sales team email address
 */
function getSalesEmail() {
    return SALES_EMAIL;
}

/**
 * Get admin email address
 */
function getAdminEmail() {
    return ADMIN_EMAIL;
}

/**
 * Send notification to admin about new submission
 */
function sendAdminNotification($type, $submissionId, $email) {
    $subject = "New {$type} Submission - Synelogics";
    $body = "
New {$type} submission received.

Submission ID: {$submissionId}
Customer Email: {$email}
Timestamp: " . date('Y-m-d H:i:s') . "

Please check the admin panel for full details.

---
Synelogics Automated Notification
";
    
    return sendEmail(getAdminEmail(), $subject, $body);
}

/**
 * Create HTML email template
 */
function createHtmlEmailTemplate($content, $title = 'Synelogics') {
    return "
<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>{$title}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            color: white;
            padding: 30px 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background: #ffffff;
            padding: 30px 20px;
            border: 1px solid #e5e7eb;
        }
        .footer {
            background: #f9fafb;
            padding: 20px;
            text-align: center;
            border-radius: 0 0 8px 8px;
            border: 1px solid #e5e7eb;
            border-top: none;
        }
        .button {
            display: inline-block;
            background: #3b82f6;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 0;
        }
        .highlight {
            background: #f0f9ff;
            padding: 15px;
            border-left: 4px solid #3b82f6;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class='header'>
        <h1>Synelogics</h1>
        <p>Innovative IT Solutions</p>
    </div>
    <div class='content'>
        {$content}
    </div>
    <div class='footer'>
        <p><strong>Synelogics</strong><br>
        Email: <EMAIL><br>
        Phone: +****************<br>
        Website: <a href='https://synelogics.com'>synelogics.com</a></p>
        <p style='font-size: 12px; color: #6b7280; margin-top: 15px;'>
            This email was sent from Synelogics. If you have any questions, please contact us directly.
        </p>
    </div>
</body>
</html>
";
}

/**
 * Send welcome email to new consultation requests
 */
function sendConsultationWelcomeEmail($email, $name, $serviceName) {
    $content = "
        <h2>Thank you for your consultation request!</h2>
        <p>Dear {$name},</p>
        <p>We have received your consultation request for <strong>{$serviceName}</strong> and are excited to discuss your project with you.</p>
        
        <div class='highlight'>
            <h3>What happens next:</h3>
            <ol>
                <li>Our team will review your requirements within 24 hours</li>
                <li>We'll contact you to schedule your free consultation</li>
                <li>During the consultation, we'll provide expert recommendations</li>
                <li>You'll receive a custom proposal with timeline and pricing</li>
            </ol>
        </div>
        
        <p>If you have any urgent questions, please don't hesitate to contact us:</p>
        <p>
            <a href='tel:+15551234567' class='button'>Call Us: +****************</a>
        </p>
        
        <p>We look forward to speaking with you soon!</p>
        <p>Best regards,<br>The Synelogics Team</p>
    ";
    
    $htmlBody = createHtmlEmailTemplate($content, 'Consultation Request Received');
    $subject = 'Thank you for your consultation request - Synelogics';
    
    return sendEmail($email, $subject, $htmlBody, true);
}

/**
 * Log email activity
 */
function logEmailActivity($to, $subject, $status) {
    $logEntry = date('Y-m-d H:i:s') . " - Email to: {$to}, Subject: {$subject}, Status: " . ($status ? 'SUCCESS' : 'FAILED') . "\n";
    
    $logDir = __DIR__ . '/../logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logDir . '/email.log', $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * Validate email configuration
 */
function validateEmailConfig() {
    $errors = [];
    
    if (SMTP_USERNAME === '<EMAIL>') {
        $errors[] = 'SMTP username not configured';
    }
    
    if (SMTP_PASSWORD === 'your-app-password') {
        $errors[] = 'SMTP password not configured';
    }
    
    if (SALES_EMAIL === '<EMAIL>') {
        $errors[] = 'Sales email not configured';
    }
    
    return $errors;
}

// Initialize email configuration check
$emailConfigErrors = validateEmailConfig();
if (!empty($emailConfigErrors)) {
    error_log('Email configuration warnings: ' . implode(', ', $emailConfigErrors));
}
?>
