<?php
// Include configuration for base path handling
require_once __DIR__ . '/config.php';

// Include access control system (TEMPORARY - Remove when site goes live)
require_once __DIR__ . '/access-control.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title : 'Synelogics - Innovative IT Solutions'; ?></title>
    <meta name="description" content="<?php echo isset($meta_description) ? $meta_description : 'Synelogics provides innovative IT solutions including web development, ERP/CRM systems, cloud migrations, and digital transformation services across North America and the Middle East.'; ?>">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Alpine.js for interactive components -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo asset('assets/css/style.css'); ?>">
    
    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'primary': {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                        'secondary': {
                            50: '#f0fdfa',
                            100: '#ccfbf1',
                            200: '#99f6e4',
                            300: '#5eead4',
                            400: '#2dd4bf',
                            500: '#14b8a6',
                            600: '#0d9488',
                            700: '#0f766e',
                            800: '#115e59',
                            900: '#134e4a',
                        }
                    }
                }
            }
        }
    </script>
    
    <style>
        /* Additional inline styles for immediate loading */
        .fade-in { animation: fadeIn 0.6s ease-in; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }

        /* Services dropdown - FORCE perfect centering with high specificity */
        .services-dropdown,
        div.services-dropdown,
        [class*="services-dropdown"] {
            position: absolute !important;
            left: 50% !important;
            transform: translateX(-50%) !important;
            top: 100% !important;
            margin-top: 0.5rem !important;
            margin-left: 0 !important;
            margin-right: 0 !important;
            right: auto !important;
            width: 95vw !important;
            max-width: 75rem !important;
            background: white !important;
            border-radius: 0.5rem !important;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
            border: 1px solid #e5e7eb !important;
            z-index: 50 !important;
            /* FORCE centered animation origin */
            transform-origin: center center !important;
        }

        /* CSS-based scale animation for smooth performance */
        .services-dropdown {
            animation: dropdownFadeIn 0.3s ease-out;
        }

        @keyframes dropdownFadeIn {
            from {
                opacity: 0;
                transform: translateX(-50%) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateX(-50%) scale(1);
            }
        }

        /* Responsive adjustments - maintain centered animation */
        @media (min-width: 1024px) {
            .services-dropdown {
                max-width: 70rem !important;
            }
        }

        @media (min-width: 1280px) {
            .services-dropdown {
                max-width: 75rem !important;
            }
        }

        @media (max-width: 1023px) {
            .services-dropdown {
                position: static !important;
                transform: none !important;
                width: 100% !important;
                max-width: none !important;
                margin-top: 0 !important;
                box-shadow: none !important;
                border: none !important;
                border-radius: 0 !important;
            }
        }

        /* Hero section optimized for 16:9 viewport */
        .hero-gradient {
            min-height: 50vh;
            position: relative;
            overflow: hidden;
        }
        @media (min-width: 768px) {
            .hero-gradient { min-height: 55vh; }
        }
        @media (min-width: 1024px) {
            .hero-gradient { min-height: 60vh; }
        }

        /* Hero decorative elements */
        .hero-gradient::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 40%;
            height: 200%;
            background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(20, 184, 166, 0.1));
            border-radius: 50%;
            transform: rotate(15deg);
            z-index: 1;
        }

        .hero-gradient::after {
            content: '';
            position: absolute;
            bottom: -30%;
            left: -15%;
            width: 30%;
            height: 150%;
            background: linear-gradient(-45deg, rgba(20, 184, 166, 0.08), rgba(59, 130, 246, 0.08));
            border-radius: 50%;
            transform: rotate(-20deg);
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        /* Floating elements animation */
        .hero-floating-element {
            position: absolute;
            opacity: 0.6;
            animation: float 6s ease-in-out infinite;
        }

        .hero-floating-element:nth-child(1) {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .hero-floating-element:nth-child(2) {
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .hero-floating-element:nth-child(3) {
            bottom: 30%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(5deg); }
        }

        /* Hide floating elements on small screens to avoid clutter */
        @media (max-width: 768px) {
            .hero-floating-element {
                display: none;
            }

            .hero-gradient::before,
            .hero-gradient::after {
                opacity: 0.5;
            }
        }

        /* Adjust hero badge for mobile */
        @media (max-width: 640px) {
            .hero-badge {
                font-size: 0.75rem;
                padding: 0.375rem 0.75rem;
            }
        }
    </style>
</head>
<body class="bg-white text-gray-900 antialiased min-h-screen flex flex-col">
    <?php include 'nav.php'; ?>
