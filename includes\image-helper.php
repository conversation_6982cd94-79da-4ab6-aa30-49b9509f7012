<?php
/**
 * Image Helper for Synelogics Website
 * Integrates with images.php API to get relevant images
 */

/**
 * Get image URL from the images API
 * @param string $query - Search query for the image
 * @param int $width - Desired width (default: 800)
 * @param int $height - Desired height (default: 600)
 * @return string - Image URL or placeholder
 */
function getImageUrl($query, $width = 800, $height = 600) {
    $baseUrl = '/images.php';
    $params = http_build_query([
        'query' => $query,
        'width' => $width,
        'height' => $height
    ]);
    
    return $baseUrl . '?' . $params;
}

/**
 * Get service-specific image
 * @param string $service - Service name
 * @param string $type - Type of image (hero, detail, icon, etc.)
 * @param int $width - Desired width
 * @param int $height - Desired height
 * @return string - Image URL
 */
function getServiceImage($service, $type = 'hero', $width = 800, $height = 600) {
    $queries = [
        'software-development' => [
            'hero' => 'software development coding programming',
            'detail' => 'web development mobile app coding',
            'icon' => 'programming code development'
        ],
        'web-mobile-development' => [
            'hero' => 'web development mobile app responsive design',
            'detail' => 'smartphone laptop coding interface',
            'portfolio' => 'mobile app interface design'
        ],
        'erp-crm-development' => [
            'hero' => 'business software enterprise system dashboard',
            'detail' => 'crm dashboard analytics business',
            'interface' => 'business management software interface'
        ],
        'cloud-devops' => [
            'hero' => 'cloud computing server infrastructure',
            'detail' => 'devops automation pipeline deployment',
            'aws' => 'amazon web services cloud computing'
        ],
        'data-analytics' => [
            'hero' => 'data analytics business intelligence dashboard',
            'detail' => 'charts graphs data visualization',
            'warehouse' => 'data warehouse big data analytics'
        ],
        'cybersecurity' => [
            'hero' => 'cybersecurity network security protection',
            'detail' => 'security shield protection cyber',
            'monitoring' => 'security monitoring network protection'
        ],
        'managed-it' => [
            'hero' => 'IT support helpdesk technical assistance',
            'detail' => 'server management infrastructure monitoring',
            'support' => 'technical support IT helpdesk'
        ]
    ];
    
    $query = $queries[$service][$type] ?? $queries[$service]['hero'] ?? 'technology business professional';
    return getImageUrl($query, $width, $height);
}

/**
 * Get portfolio/project image
 * @param string $project - Project type
 * @param int $width - Desired width
 * @param int $height - Desired height
 * @return string - Image URL
 */
function getPortfolioImage($project, $width = 400, $height = 300) {
    $queries = [
        'ecommerce' => 'ecommerce online shopping mobile app',
        'healthcare' => 'healthcare medical technology telemedicine',
        'fintech' => 'financial technology banking app dashboard',
        'education' => 'education learning management system',
        'logistics' => 'logistics supply chain management',
        'manufacturing' => 'manufacturing industry automation'
    ];
    
    $query = $queries[$project] ?? 'business technology application';
    return getImageUrl($query, $width, $height);
}

/**
 * Get technology/tool image
 * @param string $tech - Technology name
 * @param int $width - Desired width
 * @param int $height - Desired height
 * @return string - Image URL
 */
function getTechImage($tech, $width = 100, $height = 100) {
    $queries = [
        'react' => 'react javascript framework development',
        'nodejs' => 'nodejs javascript backend development',
        'python' => 'python programming language development',
        'aws' => 'amazon web services cloud computing',
        'azure' => 'microsoft azure cloud platform',
        'docker' => 'docker container technology devops',
        'kubernetes' => 'kubernetes container orchestration',
        'mongodb' => 'mongodb database nosql',
        'postgresql' => 'postgresql database sql',
        'tableau' => 'tableau data visualization analytics',
        'powerbi' => 'power bi microsoft business intelligence'
    ];
    
    $query = $queries[strtolower($tech)] ?? $tech . ' technology logo';
    return getImageUrl($query, $width, $height);
}

/**
 * Get team/about image
 * @param string $type - Type of team image
 * @param int $width - Desired width
 * @param int $height - Desired height
 * @return string - Image URL
 */
function getTeamImage($type = 'team', $width = 600, $height = 400) {
    $queries = [
        'team' => 'professional team business technology',
        'office' => 'modern office workspace technology',
        'meeting' => 'business meeting collaboration teamwork',
        'developer' => 'software developer programming coding',
        'consultant' => 'business consultant technology advisor'
    ];
    
    $query = $queries[$type] ?? 'professional business team';
    return getImageUrl($query, $width, $height);
}

/**
 * Get industry-specific image
 * @param string $industry - Industry name
 * @param int $width - Desired width
 * @param int $height - Desired height
 * @return string - Image URL
 */
function getIndustryImage($industry, $width = 600, $height = 400) {
    $queries = [
        'healthcare' => 'healthcare medical technology hospital',
        'finance' => 'finance banking financial services',
        'retail' => 'retail ecommerce shopping technology',
        'manufacturing' => 'manufacturing industry automation',
        'education' => 'education technology learning digital',
        'government' => 'government public sector technology',
        'nonprofit' => 'nonprofit organization community service'
    ];
    
    $query = $queries[strtolower($industry)] ?? $industry . ' industry technology';
    return getImageUrl($query, $width, $height);
}

/**
 * Create image tag with proper attributes
 * @param string $src - Image source URL
 * @param string $alt - Alt text
 * @param string $class - CSS classes
 * @param array $attributes - Additional attributes
 * @return string - HTML img tag
 */
function createImageTag($src, $alt, $class = '', $attributes = []) {
    $attrs = [];
    $attrs[] = 'src="' . htmlspecialchars($src) . '"';
    $attrs[] = 'alt="' . htmlspecialchars($alt) . '"';
    
    if ($class) {
        $attrs[] = 'class="' . htmlspecialchars($class) . '"';
    }
    
    // Add loading="lazy" for performance
    $attrs[] = 'loading="lazy"';
    
    // Add additional attributes
    foreach ($attributes as $key => $value) {
        $attrs[] = htmlspecialchars($key) . '="' . htmlspecialchars($value) . '"';
    }
    
    return '<img ' . implode(' ', $attrs) . '>';
}

/**
 * Create responsive image with multiple sizes
 * @param string $query - Search query
 * @param string $alt - Alt text
 * @param string $class - CSS classes
 * @return string - HTML img tag with srcset
 */
function createResponsiveImage($query, $alt, $class = '') {
    $sizes = [
        ['width' => 400, 'height' => 300],
        ['width' => 800, 'height' => 600],
        ['width' => 1200, 'height' => 900]
    ];
    
    $srcset = [];
    $defaultSrc = '';
    
    foreach ($sizes as $i => $size) {
        $url = getImageUrl($query, $size['width'], $size['height']);
        if ($i === 1) { // Use medium size as default
            $defaultSrc = $url;
        }
        $srcset[] = $url . ' ' . $size['width'] . 'w';
    }
    
    $attributes = [
        'srcset' => implode(', ', $srcset),
        'sizes' => '(max-width: 768px) 400px, (max-width: 1200px) 800px, 1200px'
    ];
    
    return createImageTag($defaultSrc, $alt, $class, $attributes);
}

/**
 * Get placeholder image with icon
 * @param string $icon - FontAwesome icon class
 * @param string $text - Placeholder text
 * @param int $width - Width
 * @param int $height - Height
 * @return string - HTML for placeholder
 */
function getPlaceholderImage($icon, $text, $width = 400, $height = 300) {
    return '
    <div class="img-placeholder" style="width: ' . $width . 'px; height: ' . $height . 'px;">
        <div class="text-center">
            <i class="' . htmlspecialchars($icon) . ' text-4xl text-gray-400 mb-4"></i>
            <p class="text-gray-500">' . htmlspecialchars($text) . '</p>
        </div>
    </div>';
}
?>
