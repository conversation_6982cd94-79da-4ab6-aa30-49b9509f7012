<?php
/**
 * Access Control Removal Script
 * Run this script once to remove the access control system when the site goes live
 * 
 * WARNING: This script will make permanent changes to your files.
 * Make sure to backup your files before running this script.
 */

// Security check - only run if specifically requested
if (!isset($_GET['confirm']) || $_GET['confirm'] !== 'remove-access-control') {
    die('Access denied. Use ?confirm=remove-access-control to run this script.');
}

echo "<h1>Synelogics Access Control Removal</h1>";
echo "<p>Starting removal process...</p>";

$errors = [];
$success = [];

// 1. Remove access control from header.php
echo "<h3>1. Removing access control from header.php...</h3>";
$header_file = 'includes/header.php';
if (file_exists($header_file)) {
    $header_content = file_get_contents($header_file);
    
    // Remove the access control lines
    $old_content = "// Include configuration for base path handling
require_once __DIR__ . '/config.php';

// Include access control system (TEMPORARY - Remove when site goes live)
require_once __DIR__ . '/access-control.php';";
    
    $new_content = "// Include configuration for base path handling
require_once __DIR__ . '/config.php';";
    
    $updated_content = str_replace($old_content, $new_content, $header_content);
    
    if (file_put_contents($header_file, $updated_content)) {
        $success[] = "✓ Removed access control from header.php";
    } else {
        $errors[] = "✗ Failed to update header.php";
    }
} else {
    $errors[] = "✗ header.php not found";
}

// 2. Update .htaccess file
echo "<h3>2. Updating .htaccess file...</h3>";
$htaccess_file = '.htaccess';
if (file_exists($htaccess_file)) {
    $htaccess_content = file_get_contents($htaccess_file);
    
    // Remove the access control line
    $old_line = "# Access control (TEMPORARY - Remove when site goes live)\nRewriteRule ^access/?$ access.php [L]\n\n";
    $updated_content = str_replace($old_line, "", $htaccess_content);
    
    if (file_put_contents($htaccess_file, $updated_content)) {
        $success[] = "✓ Removed access control rules from .htaccess";
    } else {
        $errors[] = "✗ Failed to update .htaccess";
    }
} else {
    $errors[] = "✗ .htaccess not found";
}

// 3. Remove access control files
echo "<h3>3. Removing access control files...</h3>";
$files_to_remove = [
    'access.php',
    'includes/access-control.php',
    'remove-access-control.php' // Remove this script itself
];

foreach ($files_to_remove as $file) {
    if (file_exists($file)) {
        if (unlink($file)) {
            $success[] = "✓ Removed $file";
        } else {
            $errors[] = "✗ Failed to remove $file";
        }
    } else {
        $success[] = "✓ $file already removed or not found";
    }
}

// 4. Clear any existing sessions
echo "<h3>4. Clearing sessions...</h3>";
session_start();
session_destroy();
$success[] = "✓ Cleared all sessions";

// Display results
echo "<h2>Removal Results</h2>";

if (!empty($success)) {
    echo "<h3 style='color: green;'>Successful Operations:</h3>";
    echo "<ul>";
    foreach ($success as $msg) {
        echo "<li style='color: green;'>$msg</li>";
    }
    echo "</ul>";
}

if (!empty($errors)) {
    echo "<h3 style='color: red;'>Errors:</h3>";
    echo "<ul>";
    foreach ($errors as $msg) {
        echo "<li style='color: red;'>$msg</li>";
    }
    echo "</ul>";
} else {
    echo "<h3 style='color: green;'>✓ Access control system successfully removed!</h3>";
    echo "<p>Your website is now publicly accessible without any access code requirements.</p>";
    echo "<p><strong>Note:</strong> This removal script has deleted itself as part of the cleanup process.</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Return to Homepage</a></p>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Access Control Removal - Synelogics</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        h1 { color: #333; }
        h2 { color: #666; border-bottom: 2px solid #eee; padding-bottom: 10px; }
        h3 { color: #888; }
        ul { background: #f9f9f9; padding: 15px; border-radius: 5px; }
        li { margin: 5px 0; }
        hr { margin: 30px 0; }
        a { color: #007cba; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <!-- Content is generated by PHP above -->
</body>
</html>
