<?php
/**
 * PHP Router for Development Server
 * This file handles clean URL routing when using PHP's built-in development server
 * Usage: php -S localhost:8000 router.php
 */

// Check if the request is for a real file (CSS, JS, images, etc.)
if (file_exists($_SERVER['DOCUMENT_ROOT'] . $_SERVER['REQUEST_URI'])) {
    return false; // Let the server handle it
}

// Get the requested URI
$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$uri = rtrim($uri, '/');

// If the URI is empty, serve index.php
if (empty($uri) || $uri === '/') {
    require_once 'index.php';
    exit;
}

// Remove leading slash for easier matching
$path = ltrim($uri, '/');

// Simple routing with direct file checks
switch ($path) {
    // Main pages
    case 'about':
        if (file_exists('about.php')) {
            require_once 'about.php';
            exit;
        }
        break;

    case 'contact':
        if (file_exists('contact.php')) {
            require_once 'contact.php';
            exit;
        }
        break;

    case 'industries':
        if (file_exists('industries.php')) {
            require_once 'industries.php';
            exit;
        }
        break;

    case 'testimonials':
        if (file_exists('testimonials.php')) {
            require_once 'testimonials.php';
            exit;
        }
        break;

    case 'privacy-policy':
        if (file_exists('privacy-policy.php')) {
            require_once 'privacy-policy.php';
            exit;
        }
        break;

    case 'terms-of-service':
        if (file_exists('terms-of-service.php')) {
            require_once 'terms-of-service.php';
            exit;
        }
        break;

    case 'url-test':
        if (file_exists('url-test.php')) {
            require_once 'url-test.php';
            exit;
        }
        break;

    // Form handlers
    case 'contact-handler':
        if (file_exists('contact-handler.php')) {
            require_once 'contact-handler.php';
            exit;
        }
        break;

    case 'consultation-handler':
        if (file_exists('consultation-handler.php')) {
            require_once 'consultation-handler.php';
            exit;
        }
        break;

    // Services main page
    case 'services':
        if (file_exists('services/index.php')) {
            require_once 'services/index.php';
            exit;
        }
        break;

    // Service categories - redirect non-slash to slash version
    case 'services/software-development':
        header("Location: services/software-development/", true, 301);
        exit;

    case 'services/cloud-devops':
        header("Location: services/cloud-devops/", true, 301);
        exit;

    case 'services/data-analytics':
        header("Location: services/data-analytics/", true, 301);
        exit;

    case 'services/cybersecurity':
        header("Location: services/cybersecurity/", true, 301);
        exit;

    case 'services/managed-it':
        header("Location: services/managed-it/", true, 301);
        exit;

    // Service categories with trailing slash (preferred URLs)
    case 'services/software-development/':
        if (file_exists('services/software-development.php')) {
            require_once 'services/software-development.php';
            exit;
        }
        break;

    case 'services/cloud-devops/':
        if (file_exists('services/cloud-devops.php')) {
            require_once 'services/cloud-devops.php';
            exit;
        }
        break;

    case 'services/data-analytics/':
        if (file_exists('services/data-analytics.php')) {
            require_once 'services/data-analytics.php';
            exit;
        }
        break;

    case 'services/cybersecurity/':
        if (file_exists('services/cybersecurity.php')) {
            require_once 'services/cybersecurity.php';
            exit;
        }
        break;

    case 'services/managed-it/':
        if (file_exists('services/managed-it.php')) {
            require_once 'services/managed-it.php';
            exit;
        }
        break;

    // Individual service pages
    case 'services/software-development/web-mobile-development':
        if (file_exists('services/software-development/web-mobile-development.php')) {
            require_once 'services/software-development/web-mobile-development.php';
            exit;
        }
        break;
}

// Handle .php extension redirects (redirect to clean URL)
if (preg_match('/^(.+)\.php$/', $path, $matches)) {
    $cleanPath = $matches[1];
    header("Location: $cleanPath", true, 301);
    exit;
}

// If no route matches, show 404
http_response_code(404);
if (file_exists('404.php')) {
    require_once '404.php';
} else {
    echo '<!DOCTYPE html>
<html>
<head>
    <title>404 - Page Not Found</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        h1 { color: #e74c3c; }
    </style>
</head>
<body>
    <h1>404 - Page Not Found</h1>
    <p>The requested page "' . htmlspecialchars($path) . '" could not be found.</p>
    <a href="./">Return to Homepage</a>
</body>
</html>';
}
?>
