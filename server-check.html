<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Setup Check - Synelogics</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            line-height: 1.6;
        }
        .step {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007cba;
        }
        .error {
            background: #ffe6e6;
            border-left-color: #dc3545;
        }
        .success {
            background: #e6ffe6;
            border-left-color: #28a745;
        }
        code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
        }
        .test-links {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 20px 0;
        }
        .test-links a {
            display: block;
            padding: 10px;
            background: #007cba;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            text-align: center;
        }
        .test-links a:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <h1>Synelogics Server Setup Check</h1>
    
    <div class="step error">
        <h2>🚨 Current Issue</h2>
        <p>Clean URLs (without .php) are not working. This means the router is not being used properly.</p>
    </div>
    
    <div class="step">
        <h2>📋 Step 1: Check Current Server</h2>
        <p>First, let's see what's happening:</p>
        <div class="test-links">
            <a href="/debug.php">Check Debug Info</a>
            <a href="/debug">Test Clean URL</a>
        </div>
        <p>Click "Check Debug Info" to see detailed server information.</p>
    </div>
    
    <div class="step">
        <h2>🔧 Step 2: Fix the Server</h2>
        <p><strong>Stop your current server</strong> (press Ctrl+C in the terminal)</p>
        <p><strong>Then start with the router:</strong></p>
        <code>php -S localhost:8000 simple-router.php</code>
        
        <h3>Alternative Methods:</h3>
        <ul>
            <li><strong>Windows:</strong> Double-click <code>start-server.bat</code></li>
            <li><strong>Mac/Linux:</strong> Run <code>./start-server.sh</code></li>
        </ul>
    </div>
    
    <div class="step">
        <h2>🧪 Step 3: Test Clean URLs</h2>
        <p>After restarting with the router, test these URLs:</p>
        <div class="test-links">
            <a href="/about">Test /about</a>
            <a href="/contact">Test /contact</a>
            <a href="/services">Test /services</a>
            <a href="/services/software-development">Test Service Page</a>
        </div>
    </div>
    
    <div class="step">
        <h2>🔍 Step 4: Verify Router is Working</h2>
        <p>If the router is working correctly, you should see:</p>
        <ul>
            <li>✅ Clean URLs load without .php extension</li>
            <li>✅ URLs with .php redirect to clean URLs</li>
            <li>✅ Debug page shows "Router is being used"</li>
        </ul>
    </div>
    
    <div class="step error">
        <h2>🆘 If Still Not Working</h2>
        <p>If clean URLs still don't work after following the steps above:</p>
        <ol>
            <li>Check the debug page: <a href="/debug.php">/debug.php</a></li>
            <li>Verify files exist and router is detected</li>
            <li>Try accessing pages directly with .php extension temporarily</li>
            <li>Check the terminal for any error messages</li>
        </ol>
    </div>
    
    <div class="step success">
        <h2>✅ Alternative: Direct Access</h2>
        <p>If you need to access pages immediately while fixing the router:</p>
        <div class="test-links">
            <a href="/about.php">About (direct)</a>
            <a href="/contact.php">Contact (direct)</a>
            <a href="/services/software-development.php">Services (direct)</a>
        </div>
    </div>
    
    <script>
        // Add some JavaScript to test if we're using a router
        console.log('Current URL:', window.location.href);
        console.log('Pathname:', window.location.pathname);
        
        if (window.location.pathname === '/server-check.html') {
            console.log('❌ Accessing static HTML file - router not in use');
        } else {
            console.log('✅ Router might be in use');
        }
    </script>
</body>
</html>
