<?php
$page_title = "CI/CD Pipeline Development | DevOps Automation | Synelogics";
$meta_description = "Professional CI/CD pipeline development services. Automate your software delivery with Jenkins, GitLab CI, GitHub Actions, and Azure DevOps.";
include '../../includes/header.php';
include '../../includes/image-helper.php';
?>

<!-- Hero Section -->
<section class="hero-gradient py-24">
    <div class="container-custom">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div class="fade-in-on-scroll">
                <div class="inline-block bg-green-100 text-green-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
                    Cloud & DevOps
                </div>
                <h1 class="text-hero text-gradient mb-6">CI/CD Pipeline Development</h1>
                <p class="text-xl text-gray-600 mb-8 leading-relaxed">
                    Accelerate your software delivery with automated CI/CD pipelines that ensure quality, speed, and reliability.
                </p>
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="#consultation" class="btn-primary text-center">
                        Book a Consultation
                    </a>
                    <a href="#features" class="btn-outline text-center">
                        View Features
                    </a>
                </div>
            </div>
            <div class="fade-in-on-scroll delay-200">
                <div class="relative">
                    <?php echo createImageTag(
                        getServiceImage('cicd-pipelines', 'hero', 800, 600),
                        'CI/CD Pipeline Services',
                        'rounded-2xl shadow-2xl w-full h-80 lg:h-96 object-cover'
                    ); ?>
                    <div class="absolute inset-0 bg-gradient-to-tr from-green-500/20 to-blue-500/20 rounded-lg"></div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Overview -->
<section class="section-padding bg-white">
    <div class="container-custom">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Sidebar Navigation -->
            <div class="lg:col-span-1">
                <div class="bg-gray-50 rounded-xl p-6 sticky top-24">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Our Services</h3>
                    <ul class="space-y-2">
                        <li><a href="#pipeline-design" class="block py-2 px-3 text-gray-700 hover:bg-white hover:text-primary-600 rounded-lg transition-colors">Pipeline Design</a></li>
                        <li><a href="#automated-testing" class="block py-2 px-3 text-gray-700 hover:bg-white hover:text-primary-600 rounded-lg transition-colors">Automated Testing</a></li>
                        <li><a href="#deployment-automation" class="block py-2 px-3 text-gray-700 hover:bg-white hover:text-primary-600 rounded-lg transition-colors">Deployment Automation</a></li>
                        <li><a href="#quality-gates" class="block py-2 px-3 text-gray-700 hover:bg-white hover:text-primary-600 rounded-lg transition-colors">Quality Gates</a></li>
                    </ul>

                    <div class="mt-8 p-4 bg-primary-50 rounded-lg border border-primary-200">
                        <h4 class="font-semibold text-primary-900 mb-2">Need Help?</h4>
                        <p class="text-sm text-primary-700 mb-3">Get expert guidance on CI/CD implementation</p>
                        <a href="tel:+15551234567" class="text-primary-600 font-medium text-sm">
                            <i class="fas fa-phone mr-2"></i>+****************
                        </a>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="lg:col-span-3">
                <!-- Pipeline Design -->
                <div id="pipeline-design" class="mb-12 fade-in-on-scroll">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-project-diagram text-blue-600 text-xl"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900">CI/CD Pipeline Design</h2>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                        <div>
                            <p class="text-gray-600 leading-relaxed mb-6">
                                Custom CI/CD pipeline architecture designed for your specific technology stack and deployment requirements, ensuring optimal performance and reliability.
                            </p>
                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <i class="fas fa-sitemap text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">Pipeline Architecture</span>
                                        <p class="text-sm text-gray-600">Custom workflow design for your tech stack</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <i class="fas fa-code-branch text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">Branching Strategy</span>
                                        <p class="text-sm text-gray-600">Git flow and deployment strategies</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <i class="fas fa-layer-group text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">Multi-Environment</span>
                                        <p class="text-sm text-gray-600">Dev, staging, and production pipelines</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="img-placeholder rounded-lg shadow-lg w-full h-48">
                                <div class="text-center">
                                    <i class="fas fa-project-diagram text-3xl text-gray-400 mb-2"></i>
                                    <p class="text-gray-500 text-sm">Pipeline Architecture</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Automated Testing -->
                <div id="automated-testing" class="mb-12 fade-in-on-scroll">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-vial text-green-600 text-xl"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900">Automated Testing Integration</h2>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                        <div>
                            <div class="img-placeholder rounded-lg shadow-lg w-full h-48">
                                <div class="text-center">
                                    <i class="fas fa-vial text-3xl text-gray-400 mb-2"></i>
                                    <p class="text-gray-500 text-sm">Testing Dashboard</p>
                                </div>
                            </div>
                        </div>
                        <div>
                            <p class="text-gray-600 leading-relaxed mb-6">
                                Comprehensive automated testing strategy including unit tests, integration tests, and end-to-end testing to ensure code quality and reliability.
                            </p>
                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <i class="fas fa-cube text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">Unit Testing</span>
                                        <p class="text-sm text-gray-600">Automated unit test execution and reporting</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <i class="fas fa-puzzle-piece text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">Integration Testing</span>
                                        <p class="text-sm text-gray-600">API and service integration validation</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <i class="fas fa-desktop text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">E2E Testing</span>
                                        <p class="text-sm text-gray-600">End-to-end user journey testing</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Deployment Automation -->
                <div id="deployment-automation" class="mb-12 fade-in-on-scroll">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-rocket text-purple-600 text-xl"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900">Deployment Automation</h2>
                    </div>

                    <div class="bg-gray-50 rounded-xl p-8">
                        <p class="text-gray-600 leading-relaxed mb-6">
                            Seamless deployment automation with zero-downtime deployments, rollback capabilities, and multi-environment support.
                        </p>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-shipping-fast text-blue-600 text-xl"></i>
                                </div>
                                <h4 class="font-semibold text-gray-900 mb-2">Blue-Green Deployment</h4>
                                <p class="text-sm text-gray-600">Zero-downtime deployment strategy</p>
                            </div>
                            <div class="text-center">
                                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-undo text-green-600 text-xl"></i>
                                </div>
                                <h4 class="font-semibold text-gray-900 mb-2">Rollback Automation</h4>
                                <p class="text-sm text-gray-600">Automatic rollback on deployment failures</p>
                            </div>
                            <div class="text-center">
                                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-chart-line text-purple-600 text-xl"></i>
                                </div>
                                <h4 class="font-semibold text-gray-900 mb-2">Canary Releases</h4>
                                <p class="text-sm text-gray-600">Gradual rollout with monitoring</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quality Gates -->
                <div id="quality-gates" class="mb-12 fade-in-on-scroll">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-shield-alt text-orange-600 text-xl"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900">Quality Gates & Code Analysis</h2>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                        <div>
                            <p class="text-gray-600 leading-relaxed mb-6">
                                Implement quality gates and automated code analysis to ensure only high-quality code reaches production environments.
                            </p>
                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <i class="fas fa-search text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">Code Quality Analysis</span>
                                        <p class="text-sm text-gray-600">SonarQube and static code analysis</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <i class="fas fa-bug text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">Security Scanning</span>
                                        <p class="text-sm text-gray-600">Vulnerability and dependency scanning</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <i class="fas fa-percentage text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">Coverage Thresholds</span>
                                        <p class="text-sm text-gray-600">Automated test coverage validation</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="img-placeholder rounded-lg shadow-lg w-full h-48">
                                <div class="text-center">
                                    <i class="fas fa-shield-alt text-3xl text-gray-400 mb-2"></i>
                                    <p class="text-gray-500 text-sm">Quality Gates Dashboard</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section id="features" class="py-20 bg-gray-50">
    <div class="container-custom">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">CI/CD Pipeline Services</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Streamline your development workflow with automated testing, building, and deployment processes.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Automated Testing</h3>
                <p class="text-gray-600">
                    Comprehensive automated testing including unit tests, integration tests, and end-to-end testing in your pipeline.
                </p>
            </div>
            
            <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-6">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Build Automation</h3>
                <p class="text-gray-600">
                    Automated build processes that compile, package, and prepare your applications for deployment across environments.
                </p>
            </div>
            
            <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-6">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Deployment Automation</h3>
                <p class="text-gray-600">
                    Seamless deployment to multiple environments with rollback capabilities and zero-downtime deployments.
                </p>
            </div>
            
            <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-6">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Quality Gates</h3>
                <p class="text-gray-600">
                    Implement quality gates and code analysis to ensure only high-quality code reaches production environments.
                </p>
            </div>
            
            <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-6">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h5v-5H4v5zM9 3h5v5H9V3z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Multi-Environment Support</h3>
                <p class="text-gray-600">
                    Deploy to development, staging, and production environments with environment-specific configurations.
                </p>
            </div>
            
            <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-6">
                    <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Pipeline Monitoring</h3>
                <p class="text-gray-600">
                    Real-time monitoring and reporting of pipeline performance, success rates, and deployment metrics.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Process Section -->
<section class="py-20">
    <div class="container-custom">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">Our CI/CD Process</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                A proven methodology for implementing robust CI/CD pipelines.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <span class="text-2xl font-bold text-blue-600">1</span>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Assessment</h3>
                <p class="text-gray-600">
                    Analyze your current development workflow and identify automation opportunities.
                </p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <span class="text-2xl font-bold text-green-600">2</span>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Design</h3>
                <p class="text-gray-600">
                    Create a customized CI/CD pipeline architecture tailored to your needs.
                </p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <span class="text-2xl font-bold text-purple-600">3</span>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Implementation</h3>
                <p class="text-gray-600">
                    Build and configure the pipeline with automated testing and deployment stages.
                </p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <span class="text-2xl font-bold text-orange-600">4</span>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Optimization</h3>
                <p class="text-gray-600">
                    Monitor, refine, and optimize the pipeline for maximum efficiency and reliability.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Benefits Section -->
<section class="py-20 bg-gray-50">
    <div class="container-custom">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">Benefits of CI/CD Pipelines</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Accelerate your development lifecycle with automated pipelines that ensure quality and reliability.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
            <div class="space-y-6">
                <div class="flex items-start space-x-4">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Faster Time to Market</h3>
                        <p class="text-gray-600">Deploy features and fixes rapidly with automated testing and deployment processes.</p>
                    </div>
                </div>

                <div class="flex items-start space-x-4">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Improved Code Quality</h3>
                        <p class="text-gray-600">Catch bugs early with automated testing and code quality checks in every commit.</p>
                    </div>
                </div>

                <div class="flex items-start space-x-4">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Reduced Risk</h3>
                        <p class="text-gray-600">Minimize deployment risks with automated rollback and comprehensive testing.</p>
                    </div>
                </div>
            </div>

            <div class="space-y-6">
                <div class="flex items-start space-x-4">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Enhanced Collaboration</h3>
                        <p class="text-gray-600">Improve team collaboration with standardized processes and automated workflows.</p>
                    </div>
                </div>

                <div class="flex items-start space-x-4">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Increased Productivity</h3>
                        <p class="text-gray-600">Free developers from manual tasks to focus on writing code and building features.</p>
                    </div>
                </div>

                <div class="flex items-start space-x-4">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Better Visibility</h3>
                        <p class="text-gray-600">Gain complete visibility into the development and deployment process.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Technologies Section -->
<section class="py-20">
    <div class="container-custom">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">CI/CD Technologies We Use</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Industry-leading tools and platforms for robust CI/CD pipeline implementation.
            </p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
                    <span class="text-2xl font-bold text-blue-600">J</span>
                </div>
                <h4 class="font-semibold text-gray-900">Jenkins</h4>
            </div>
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
                    <span class="text-2xl font-bold text-purple-600">GA</span>
                </div>
                <h4 class="font-semibold text-gray-900">GitHub Actions</h4>
            </div>
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
                    <span class="text-2xl font-bold text-orange-600">GL</span>
                </div>
                <h4 class="font-semibold text-gray-900">GitLab CI</h4>
            </div>
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
                    <span class="text-2xl font-bold text-blue-600">AZ</span>
                </div>
                <h4 class="font-semibold text-gray-900">Azure DevOps</h4>
            </div>
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
                    <span class="text-2xl font-bold text-green-600">TC</span>
                </div>
                <h4 class="font-semibold text-gray-900">TeamCity</h4>
            </div>
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
                    <span class="text-2xl font-bold text-red-600">D</span>
                </div>
                <h4 class="font-semibold text-gray-900">Docker</h4>
            </div>
        </div>
    </div>
</section>

<!-- Consultation Form -->
<?php include '../../includes/consultation-form.php'; ?>

<?php include '../../includes/footer.php'; ?>
