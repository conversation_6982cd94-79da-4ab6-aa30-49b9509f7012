<?php
$page_title = "Infrastructure Automation | DevOps & CI/CD | Synelogics";
$meta_description = "Professional infrastructure automation services using Terraform, Ansible, and modern DevOps practices. Automate deployments, scaling, and monitoring.";
include '../../includes/header.php';
include '../../includes/image-helper.php';
?>

<!-- Hero Section -->
<section class="hero-gradient py-24">
    <div class="container-custom">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div class="fade-in-on-scroll">
                <div class="inline-block bg-orange-100 text-orange-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
                    Cloud & DevOps
                </div>
                <h1 class="text-hero text-gradient mb-6">Infrastructure Automation</h1>
                <p class="text-xl text-gray-600 mb-8 leading-relaxed">
                    Automate your infrastructure deployment, scaling, and management with modern DevOps practices and Infrastructure as Code.
                </p>
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="#consultation" class="btn-primary text-center">
                        Book a Consultation
                    </a>
                    <a href="#features" class="btn-outline text-center">
                        View Features
                    </a>
                </div>
            </div>
            <div class="fade-in-on-scroll delay-200">
                <div class="relative">
                    <?php echo createImageTag(
                        getServiceImage('infrastructure-automation', 'hero', 800, 600),
                        'Infrastructure Automation Services',
                        'rounded-2xl shadow-2xl w-full h-80 lg:h-96 object-cover'
                    ); ?>
                    <div class="absolute inset-0 bg-gradient-to-tr from-blue-500/20 to-purple-500/20 rounded-lg"></div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Overview -->
<section class="section-padding bg-white">
    <div class="container-custom">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Sidebar Navigation -->
            <div class="lg:col-span-1">
                <div class="bg-gray-50 rounded-xl p-6 sticky top-24">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Our Services</h3>
                    <ul class="space-y-2">
                        <li><a href="#infrastructure-as-code" class="block py-2 px-3 text-gray-700 hover:bg-white hover:text-primary-600 rounded-lg transition-colors">Infrastructure as Code</a></li>
                        <li><a href="#cicd-automation" class="block py-2 px-3 text-gray-700 hover:bg-white hover:text-primary-600 rounded-lg transition-colors">CI/CD Automation</a></li>
                        <li><a href="#configuration-management" class="block py-2 px-3 text-gray-700 hover:bg-white hover:text-primary-600 rounded-lg transition-colors">Configuration Management</a></li>
                        <li><a href="#monitoring-automation" class="block py-2 px-3 text-gray-700 hover:bg-white hover:text-primary-600 rounded-lg transition-colors">Monitoring & Alerting</a></li>
                    </ul>

                    <div class="mt-8 p-4 bg-primary-50 rounded-lg border border-primary-200">
                        <h4 class="font-semibold text-primary-900 mb-2">Need Help?</h4>
                        <p class="text-sm text-primary-700 mb-3">Get expert guidance on infrastructure automation</p>
                        <a href="tel:+15551234567" class="text-primary-600 font-medium text-sm">
                            <i class="fas fa-phone mr-2"></i>+****************
                        </a>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="lg:col-span-3">
                <!-- Infrastructure as Code -->
                <div id="infrastructure-as-code" class="mb-12 fade-in-on-scroll">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-code text-blue-600 text-xl"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900">Infrastructure as Code</h2>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                        <div>
                            <p class="text-gray-600 leading-relaxed mb-6">
                                Define and manage your infrastructure using code with Terraform, CloudFormation, and other IaC tools for consistent, repeatable, and version-controlled deployments.
                            </p>
                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <i class="fas fa-file-code text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">Terraform Templates</span>
                                        <p class="text-sm text-gray-600">Multi-cloud infrastructure provisioning</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <i class="fas fa-cloud text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">CloudFormation</span>
                                        <p class="text-sm text-gray-600">AWS native infrastructure automation</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <i class="fas fa-code-branch text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">Version Control</span>
                                        <p class="text-sm text-gray-600">Git-based infrastructure management</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="img-placeholder rounded-lg shadow-lg w-full h-48">
                                <div class="text-center">
                                    <i class="fas fa-code text-3xl text-gray-400 mb-2"></i>
                                    <p class="text-gray-500 text-sm">Infrastructure Code</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- CI/CD Automation -->
                <div id="cicd-automation" class="mb-12 fade-in-on-scroll">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-sync text-green-600 text-xl"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900">CI/CD Pipeline Automation</h2>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                        <div>
                            <div class="img-placeholder rounded-lg shadow-lg w-full h-48">
                                <div class="text-center">
                                    <i class="fas fa-sync text-3xl text-gray-400 mb-2"></i>
                                    <p class="text-gray-500 text-sm">CI/CD Pipeline</p>
                                </div>
                            </div>
                        </div>
                        <div>
                            <p class="text-gray-600 leading-relaxed mb-6">
                                Automated build, test, and deployment pipelines that ensure consistent and reliable software delivery with minimal manual intervention.
                            </p>
                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <i class="fas fa-hammer text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">Automated Builds</span>
                                        <p class="text-sm text-gray-600">Continuous integration and compilation</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <i class="fas fa-vial text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">Automated Testing</span>
                                        <p class="text-sm text-gray-600">Unit, integration, and end-to-end testing</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <i class="fas fa-rocket text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">Automated Deployment</span>
                                        <p class="text-sm text-gray-600">Zero-downtime production deployments</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Configuration Management -->
                <div id="configuration-management" class="mb-12 fade-in-on-scroll">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-cogs text-purple-600 text-xl"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900">Configuration Management</h2>
                    </div>

                    <div class="bg-gray-50 rounded-xl p-8">
                        <p class="text-gray-600 leading-relaxed mb-6">
                            Automate server configuration and application deployment using industry-leading tools for consistent and reliable environments.
                        </p>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-cube text-red-600 text-xl"></i>
                                </div>
                                <h4 class="font-semibold text-gray-900 mb-2">Ansible</h4>
                                <p class="text-sm text-gray-600">Agentless automation and configuration management</p>
                            </div>
                            <div class="text-center">
                                <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-robot text-orange-600 text-xl"></i>
                                </div>
                                <h4 class="font-semibold text-gray-900 mb-2">Puppet</h4>
                                <p class="text-sm text-gray-600">Declarative configuration management at scale</p>
                            </div>
                            <div class="text-center">
                                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-utensils text-green-600 text-xl"></i>
                                </div>
                                <h4 class="font-semibold text-gray-900 mb-2">Chef</h4>
                                <p class="text-sm text-gray-600">Infrastructure automation and compliance</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Monitoring & Alerting -->
                <div id="monitoring-automation" class="mb-12 fade-in-on-scroll">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-chart-line text-orange-600 text-xl"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900">Monitoring & Alerting Automation</h2>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                        <div>
                            <p class="text-gray-600 leading-relaxed mb-6">
                                Comprehensive monitoring solutions with automated alerting and response systems to ensure optimal performance and rapid issue resolution.
                            </p>
                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <i class="fas fa-eye text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">Real-time Monitoring</span>
                                        <p class="text-sm text-gray-600">24/7 infrastructure and application monitoring</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <i class="fas fa-bell text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">Smart Alerting</span>
                                        <p class="text-sm text-gray-600">Intelligent alerts with escalation policies</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <i class="fas fa-magic text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">Auto-remediation</span>
                                        <p class="text-sm text-gray-600">Automated response to common issues</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="img-placeholder rounded-lg shadow-lg w-full h-48">
                                <div class="text-center">
                                    <i class="fas fa-chart-line text-3xl text-gray-400 mb-2"></i>
                                    <p class="text-gray-500 text-sm">Monitoring Dashboard</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section id="features" class="py-20 bg-gray-50">
    <div class="container-custom">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">Infrastructure Automation Services</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Streamline your operations with automated infrastructure management and deployment pipelines.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Infrastructure as Code</h3>
                <p class="text-gray-600">
                    Define and manage your infrastructure using Terraform, CloudFormation, and other IaC tools for consistent, repeatable deployments.
                </p>
            </div>
            
            <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-6">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">CI/CD Pipelines</h3>
                <p class="text-gray-600">
                    Automated build, test, and deployment pipelines using Jenkins, GitLab CI, GitHub Actions, and Azure DevOps.
                </p>
            </div>
            
            <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-6">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Configuration Management</h3>
                <p class="text-gray-600">
                    Automate server configuration and application deployment using Ansible, Puppet, and Chef for consistent environments.
                </p>
            </div>
            
            <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-6">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Auto-Scaling</h3>
                <p class="text-gray-600">
                    Implement intelligent auto-scaling solutions that automatically adjust resources based on demand and performance metrics.
                </p>
            </div>
            
            <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-6">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Monitoring & Alerting</h3>
                <p class="text-gray-600">
                    Comprehensive monitoring solutions with Prometheus, Grafana, and custom alerting systems for proactive issue resolution.
                </p>
            </div>
            
            <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-6">
                    <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Container Orchestration</h3>
                <p class="text-gray-600">
                    Deploy and manage containerized applications using Kubernetes, Docker Swarm, and container automation tools.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Process Section -->
<section class="py-20">
    <div class="container-custom">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">Our Automation Process</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                A systematic approach to implementing infrastructure automation that ensures reliability and scalability.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <span class="text-2xl font-bold text-blue-600">1</span>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Assessment</h3>
                <p class="text-gray-600">
                    Analyze current infrastructure and identify automation opportunities and requirements.
                </p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <span class="text-2xl font-bold text-green-600">2</span>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Design</h3>
                <p class="text-gray-600">
                    Create automation architecture and select appropriate tools and technologies.
                </p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <span class="text-2xl font-bold text-purple-600">3</span>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Implementation</h3>
                <p class="text-gray-600">
                    Deploy automation tools and configure infrastructure as code templates.
                </p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <span class="text-2xl font-bold text-orange-600">4</span>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Optimization</h3>
                <p class="text-gray-600">
                    Monitor performance and continuously improve automation processes.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Benefits Section -->
<section class="py-20 bg-gray-50">
    <div class="container-custom">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">Benefits of Infrastructure Automation</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Transform your operations with automated infrastructure management that delivers consistency and efficiency.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
            <div class="space-y-6">
                <div class="flex items-start space-x-4">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Reduced Manual Errors</h3>
                        <p class="text-gray-600">Eliminate human errors with automated, repeatable infrastructure deployments.</p>
                    </div>
                </div>

                <div class="flex items-start space-x-4">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Faster Deployments</h3>
                        <p class="text-gray-600">Deploy infrastructure in minutes instead of hours or days with automation.</p>
                    </div>
                </div>

                <div class="flex items-start space-x-4">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Consistent Environments</h3>
                        <p class="text-gray-600">Ensure identical configurations across development, staging, and production.</p>
                    </div>
                </div>
            </div>

            <div class="space-y-6">
                <div class="flex items-start space-x-4">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Cost Optimization</h3>
                        <p class="text-gray-600">Reduce operational costs through efficient resource utilization and automation.</p>
                    </div>
                </div>

                <div class="flex items-start space-x-4">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Improved Scalability</h3>
                        <p class="text-gray-600">Scale infrastructure automatically based on demand and business requirements.</p>
                    </div>
                </div>

                <div class="flex items-start space-x-4">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Enhanced Security</h3>
                        <p class="text-gray-600">Implement security best practices consistently across all infrastructure.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Technologies Section -->
<section class="py-20">
    <div class="container-custom">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">Automation Technologies We Use</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Leading infrastructure automation tools and platforms for reliable, scalable deployments.
            </p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
                    <span class="text-2xl font-bold text-purple-600">TF</span>
                </div>
                <h4 class="font-semibold text-gray-900">Terraform</h4>
            </div>
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
                    <span class="text-2xl font-bold text-red-600">A</span>
                </div>
                <h4 class="font-semibold text-gray-900">Ansible</h4>
            </div>
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
                    <span class="text-2xl font-bold text-orange-600">CF</span>
                </div>
                <h4 class="font-semibold text-gray-900">CloudFormation</h4>
            </div>
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
                    <span class="text-2xl font-bold text-orange-600">P</span>
                </div>
                <h4 class="font-semibold text-gray-900">Puppet</h4>
            </div>
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
                    <span class="text-2xl font-bold text-green-600">C</span>
                </div>
                <h4 class="font-semibold text-gray-900">Chef</h4>
            </div>
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
                    <span class="text-2xl font-bold text-blue-600">K8s</span>
                </div>
                <h4 class="font-semibold text-gray-900">Kubernetes</h4>
            </div>
        </div>
    </div>
</section>

<!-- Consultation Form -->
<?php include '../../includes/consultation-form.php'; ?>

<?php include '../../includes/footer.php'; ?>
