<?php
$page_title = "Web & Mobile App Development | React, React Native | Synelogics";
$meta_description = "Professional web and mobile app development services using React, React Native, Flutter, and modern technologies. Custom applications for iOS, Android, and web platforms.";
include '../../includes/header.php';
include '../../includes/image-helper.php';
?>

<!-- Hero Section -->
<section class="hero-gradient py-24">
    <div class="container-custom">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div class="fade-in-on-scroll">
                <div class="inline-block bg-blue-100 text-blue-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
                    Software Development
                </div>
                <h1 class="text-hero text-gradient mb-6">Web & Mobile App Development</h1>
                <p class="text-xl text-gray-600 mb-8 leading-relaxed">
                    Create powerful, user-friendly applications that work seamlessly across all platforms with our expert development team.
                </p>
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="#consultation" class="btn-primary text-center">
                        Start Your Project
                    </a>
                    <a href="#portfolio" class="btn-outline text-center">
                        View Portfolio
                    </a>
                </div>
            </div>
            <div class="fade-in-on-scroll">
                <div class="relative">
                    <?php echo createImageTag(
                        getServiceImage('web-mobile-development', 'hero', 800, 600),
                        'Web and Mobile Development Services',
                        'rounded-2xl shadow-2xl w-full h-80 lg:h-96 object-cover'
                    ); ?>
                    <div class="absolute -bottom-6 -right-6 bg-white rounded-xl shadow-lg p-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                            <span class="text-sm font-medium text-gray-700">Live Development</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Overview -->
<section class="section-padding bg-white">
    <div class="container-custom">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Sidebar Navigation -->
            <div class="lg:col-span-1">
                <div class="bg-gray-50 rounded-xl p-6 sticky top-24">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Our Services</h3>
                    <ul class="space-y-2">
                        <li><a href="#web-development" class="block py-2 px-3 text-gray-700 hover:bg-white hover:text-primary-600 rounded-lg transition-colors">Web Applications</a></li>
                        <li><a href="#mobile-development" class="block py-2 px-3 text-gray-700 hover:bg-white hover:text-primary-600 rounded-lg transition-colors">Mobile Apps</a></li>
                        <li><a href="#progressive-web-apps" class="block py-2 px-3 text-gray-700 hover:bg-white hover:text-primary-600 rounded-lg transition-colors">Progressive Web Apps</a></li>
                        <li><a href="#cross-platform" class="block py-2 px-3 text-gray-700 hover:bg-white hover:text-primary-600 rounded-lg transition-colors">Cross-Platform Solutions</a></li>
                    </ul>
                    
                    <div class="mt-8 p-4 bg-primary-50 rounded-lg border border-primary-200">
                        <h4 class="font-semibold text-primary-900 mb-2">Need Help?</h4>
                        <p class="text-sm text-primary-700 mb-3">Get expert guidance on your project</p>
                        <a href="tel:+15551234567" class="text-primary-600 font-medium text-sm">
                            <i class="fas fa-phone mr-2"></i>+****************
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="lg:col-span-3">
                <!-- Web Development -->
                <div id="web-development" class="mb-12 fade-in-on-scroll">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-globe text-blue-600 text-xl"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900">Web Applications</h2>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                        <div>
                            <p class="text-gray-600 leading-relaxed mb-6">
                                Build modern, responsive web applications that deliver exceptional user experiences across all devices and browsers. Our team specializes in cutting-edge technologies and frameworks.
                            </p>
                            <ul class="space-y-3">
                                <li class="flex items-start">
                                    <i class="fas fa-check text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">React.js Applications</span>
                                        <p class="text-sm text-gray-600">Dynamic, component-based user interfaces</p>
                                    </div>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">Vue.js Development</span>
                                        <p class="text-sm text-gray-600">Progressive framework for building UIs</p>
                                    </div>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">Next.js Solutions</span>
                                        <p class="text-sm text-gray-600">Server-side rendering and static generation</p>
                                    </div>
                                </li>
                            </ul>
                        </div>
                        <div>
                            <?php echo createImageTag(
                                getServiceImage('web-mobile-development', 'detail', 600, 400),
                                'Web Development Process',
                                'rounded-lg shadow-lg w-full h-48 object-cover'
                            ); ?>
                        </div>
                    </div>
                </div>
                
                <!-- Mobile Development -->
                <div id="mobile-development" class="mb-12 fade-in-on-scroll">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-mobile-alt text-green-600 text-xl"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900">Mobile Applications</h2>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                        <div>
                            <?php echo createImageTag(
                                getServiceImage('web-mobile-development', 'portfolio', 600, 400),
                                'Mobile App Development',
                                'rounded-lg shadow-lg w-full h-48 object-cover'
                            ); ?>
                        </div>
                        <div>
                            <p class="text-gray-600 leading-relaxed mb-6">
                                Develop native and cross-platform mobile applications that provide seamless user experiences on iOS and Android devices.
                            </p>
                            <ul class="space-y-3">
                                <li class="flex items-start">
                                    <i class="fas fa-check text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">React Native Apps</span>
                                        <p class="text-sm text-gray-600">Cross-platform mobile development</p>
                                    </div>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">Flutter Development</span>
                                        <p class="text-sm text-gray-600">Google's UI toolkit for mobile apps</p>
                                    </div>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check text-green-600 mt-1 mr-3"></i>
                                    <div>
                                        <span class="font-medium text-gray-900">Native iOS & Android</span>
                                        <p class="text-sm text-gray-600">Platform-specific optimized applications</p>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- Progressive Web Apps -->
                <div id="progressive-web-apps" class="mb-12 fade-in-on-scroll">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-rocket text-purple-600 text-xl"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900">Progressive Web Apps (PWA)</h2>
                    </div>
                    
                    <div class="bg-gray-50 rounded-xl p-8">
                        <p class="text-gray-600 leading-relaxed mb-6">
                            Combine the best of web and mobile apps with Progressive Web Applications that work offline, load instantly, and provide app-like experiences.
                        </p>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-wifi text-blue-600 text-xl"></i>
                                </div>
                                <h4 class="font-semibold text-gray-900 mb-2">Offline Functionality</h4>
                                <p class="text-sm text-gray-600">Works without internet connection</p>
                            </div>
                            <div class="text-center">
                                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-bolt text-green-600 text-xl"></i>
                                </div>
                                <h4 class="font-semibold text-gray-900 mb-2">Fast Loading</h4>
                                <p class="text-sm text-gray-600">Instant loading with service workers</p>
                            </div>
                            <div class="text-center">
                                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-mobile-alt text-purple-600 text-xl"></i>
                                </div>
                                <h4 class="font-semibold text-gray-900 mb-2">App-like Experience</h4>
                                <p class="text-sm text-gray-600">Native app feel in the browser</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Cross-Platform Solutions -->
                <div id="cross-platform" class="mb-12 fade-in-on-scroll">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-layer-group text-orange-600 text-xl"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900">Cross-Platform Solutions</h2>
                    </div>
                    
                    <p class="text-gray-600 leading-relaxed mb-8">
                        Maximize your reach and minimize development costs with cross-platform solutions that work seamlessly across web, mobile, and desktop platforms.
                    </p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                            <i class="fab fa-react text-3xl text-blue-600 mb-4"></i>
                            <h4 class="font-semibold text-gray-900 mb-2">React Ecosystem</h4>
                            <p class="text-sm text-gray-600">React, React Native, Next.js</p>
                        </div>
                        <div class="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                            <i class="fas fa-mobile-alt text-3xl text-green-600 mb-4"></i>
                            <h4 class="font-semibold text-gray-900 mb-2">Flutter</h4>
                            <p class="text-sm text-gray-600">Single codebase for all platforms</p>
                        </div>
                        <div class="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                            <i class="fas fa-desktop text-3xl text-purple-600 mb-4"></i>
                            <h4 class="font-semibold text-gray-900 mb-2">Electron</h4>
                            <p class="text-sm text-gray-600">Desktop apps with web technologies</p>
                        </div>
                        <div class="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                            <i class="fas fa-code text-3xl text-orange-600 mb-4"></i>
                            <h4 class="font-semibold text-gray-900 mb-2">Hybrid Solutions</h4>
                            <p class="text-sm text-gray-600">Best of native and web</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Technologies Section -->
<section class="section-padding bg-gray-50">
    <div class="container-custom">
        <div class="text-center mb-16 fade-in-on-scroll">
            <h2 class="text-section-title text-gray-900 mb-6">Technologies We Use</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                We leverage the latest technologies and frameworks to build robust, scalable applications.
            </p>
        </div>
        
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
            <div class="text-center fade-in-on-scroll">
                <div class="w-20 h-20 bg-white rounded-xl shadow-lg flex items-center justify-center mx-auto mb-4">
                    <i class="fab fa-react text-3xl text-blue-600"></i>
                </div>
                <h4 class="font-medium text-gray-900">React</h4>
            </div>
            <div class="text-center fade-in-on-scroll">
                <div class="w-20 h-20 bg-white rounded-xl shadow-lg flex items-center justify-center mx-auto mb-4">
                    <i class="fab fa-react text-3xl text-blue-500"></i>
                </div>
                <h4 class="font-medium text-gray-900">React Native</h4>
            </div>
            <div class="text-center fade-in-on-scroll">
                <div class="w-20 h-20 bg-white rounded-xl shadow-lg flex items-center justify-center mx-auto mb-4">
                    <i class="fab fa-vuejs text-3xl text-green-600"></i>
                </div>
                <h4 class="font-medium text-gray-900">Vue.js</h4>
            </div>
            <div class="text-center fade-in-on-scroll">
                <div class="w-20 h-20 bg-white rounded-xl shadow-lg flex items-center justify-center mx-auto mb-4">
                    <i class="fab fa-angular text-3xl text-red-600"></i>
                </div>
                <h4 class="font-medium text-gray-900">Angular</h4>
            </div>
            <div class="text-center fade-in-on-scroll">
                <div class="w-20 h-20 bg-white rounded-xl shadow-lg flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-mobile-alt text-3xl text-blue-400"></i>
                </div>
                <h4 class="font-medium text-gray-900">Flutter</h4>
            </div>
            <div class="text-center fade-in-on-scroll">
                <div class="w-20 h-20 bg-white rounded-xl shadow-lg flex items-center justify-center mx-auto mb-4">
                    <i class="fab fa-node-js text-3xl text-green-600"></i>
                </div>
                <h4 class="font-medium text-gray-900">Node.js</h4>
            </div>
        </div>
    </div>
</section>

<!-- Portfolio Section -->
<section id="portfolio" class="section-padding bg-white">
    <div class="container-custom">
        <div class="text-center mb-16 fade-in-on-scroll">
            <h2 class="text-section-title text-gray-900 mb-6">Recent Projects</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Explore some of our recent web and mobile application projects.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 fade-in-on-scroll">
                <?php echo createImageTag(
                    getPortfolioImage('ecommerce', 400, 200),
                    'E-commerce Mobile App',
                    'w-full h-48 object-cover'
                ); ?>
                <div class="p-6">
                    <h4 class="text-xl font-semibold text-gray-900 mb-2">E-commerce Mobile App</h4>
                    <p class="text-gray-600 mb-4">React Native app with payment integration and real-time inventory management.</p>
                    <div class="flex flex-wrap gap-2">
                        <span class="px-3 py-1 bg-blue-100 text-blue-600 text-xs rounded-full">React Native</span>
                        <span class="px-3 py-1 bg-green-100 text-green-600 text-xs rounded-full">Node.js</span>
                        <span class="px-3 py-1 bg-purple-100 text-purple-600 text-xs rounded-full">MongoDB</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 fade-in-on-scroll">
                <?php echo createImageTag(
                    getPortfolioImage('healthcare', 400, 200),
                    'Healthcare Web Platform',
                    'w-full h-48 object-cover'
                ); ?>
                <div class="p-6">
                    <h4 class="text-xl font-semibold text-gray-900 mb-2">Healthcare Web Platform</h4>
                    <p class="text-gray-600 mb-4">HIPAA-compliant patient management system with telemedicine features.</p>
                    <div class="flex flex-wrap gap-2">
                        <span class="px-3 py-1 bg-blue-100 text-blue-600 text-xs rounded-full">React</span>
                        <span class="px-3 py-1 bg-yellow-100 text-yellow-600 text-xs rounded-full">Next.js</span>
                        <span class="px-3 py-1 bg-red-100 text-red-600 text-xs rounded-full">PostgreSQL</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 fade-in-on-scroll">
                <?php echo createImageTag(
                    getPortfolioImage('fintech', 400, 200),
                    'FinTech Progressive Web App',
                    'w-full h-48 object-cover'
                ); ?>
                <div class="p-6">
                    <h4 class="text-xl font-semibold text-gray-900 mb-2">FinTech Progressive Web App</h4>
                    <p class="text-gray-600 mb-4">Secure financial dashboard with real-time analytics and offline capabilities.</p>
                    <div class="flex flex-wrap gap-2">
                        <span class="px-3 py-1 bg-green-100 text-green-600 text-xs rounded-full">Vue.js</span>
                        <span class="px-3 py-1 bg-blue-100 text-blue-600 text-xs rounded-full">PWA</span>
                        <span class="px-3 py-1 bg-orange-100 text-orange-600 text-xs rounded-full">Firebase</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Consultation Form -->
<?php include '../../includes/consultation-form.php'; ?>

<?php include '../../includes/footer.php'; ?>
