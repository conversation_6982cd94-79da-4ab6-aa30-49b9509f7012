<?php
/**
 * Simple PHP Router for Development Server
 * Usage: php -S localhost:8000 simple-router.php
 */

// Get the requested path
$request = $_SERVER['REQUEST_URI'];
$path = parse_url($request, PHP_URL_PATH);
$path = trim($path, '/');

// Debug output
error_log("Simple Router - Request: $request");
error_log("Simple Router - Path: $path");

// If it's a real file, let the server handle it
if ($path && file_exists($path)) {
    return false;
}

// Route handling
if (empty($path)) {
    // Homepage
    require 'index.php';
} elseif ($path === 'about') {
    require 'about.php';
} elseif ($path === 'contact') {
    require 'contact.php';
} elseif ($path === 'services') {
    require 'services/index.php';
} elseif ($path === 'services/software-development') {
    header("Location: services/software-development/", true, 301);
    exit;
} elseif ($path === 'services/software-development/') {
    require 'services/software-development.php';
} elseif ($path === 'services/cloud-devops') {
    header("Location: services/cloud-devops/", true, 301);
    exit;
} elseif ($path === 'services/cloud-devops/') {
    require 'services/cloud-devops.php';
} elseif ($path === 'services/data-analytics') {
    header("Location: services/data-analytics/", true, 301);
    exit;
} elseif ($path === 'services/data-analytics/') {
    require 'services/data-analytics.php';
} elseif ($path === 'services/cybersecurity') {
    header("Location: services/cybersecurity/", true, 301);
    exit;
} elseif ($path === 'services/cybersecurity/') {
    require 'services/cybersecurity.php';
} elseif ($path === 'services/managed-it') {
    header("Location: services/managed-it/", true, 301);
    exit;
} elseif ($path === 'services/managed-it/') {
    require 'services/managed-it.php';
} elseif ($path === 'services/software-development/web-mobile-development') {
    require 'services/software-development/web-mobile-development.php';
} elseif ($path === 'test-files') {
    require 'test-files.php';
} elseif ($path === 'url-test') {
    require 'url-test.php';
} elseif ($path === 'debug') {
    require 'debug.php';
} elseif (preg_match('/^(.+)\.php$/', $path, $matches)) {
    // Redirect .php URLs to clean URLs
    header("Location: " . $matches[1], true, 301);
    exit;
} else {
    // 404
    http_response_code(404);
    echo "<h1>404 - Not Found</h1>";
    echo "<p>Path: " . htmlspecialchars($path) . "</p>";
    echo "<p><a href='./'>Go Home</a></p>";
}
?>
