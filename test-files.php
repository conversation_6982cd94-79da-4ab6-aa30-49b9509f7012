<?php
echo "<h1>File Existence Test</h1>";

$files = [
    'about.php',
    'contact.php',
    'services/index.php',
    'services/software-development.php',
    'services/cloud-devops.php',
    'services/data-analytics.php',
    'services/cybersecurity.php',
    'services/managed-it.php',
    'services/software-development/web-mobile-development.php'
];

echo "<ul>";
foreach ($files as $file) {
    $exists = file_exists($file);
    $color = $exists ? 'green' : 'red';
    $status = $exists ? 'EXISTS' : 'MISSING';
    echo "<li style='color: $color'>$file - $status</li>";
}
echo "</ul>";

echo "<h2>Current Directory: " . getcwd() . "</h2>";
echo "<h2>Request URI: " . $_SERVER['REQUEST_URI'] . "</h2>";
?>
