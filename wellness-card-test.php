<?php
$page_title = "Wellness Card Visibility Test | Synelogics";
$meta_description = "Testing Wellness card visibility at different zoom levels";
include 'includes/header.php';
?>

<section class="section-padding bg-white">
    <div class="container-custom">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-4xl font-bold text-gray-900 mb-8">🔧 Wellness Card Visibility Test</h1>
            
            <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
                <h2 class="text-xl font-semibold text-green-900 mb-4">✅ Fixes Applied</h2>
                <p class="text-green-800 mb-4">
                    The Industries carousel has been simplified and the Wellness card visibility issue has been fixed.
                </p>
                <ul class="text-green-800 space-y-2">
                    <li>• <strong>Removed infinite sliding:</strong> No more auto-advance or infinite loop</li>
                    <li>• <strong>Removed auto-sliding:</strong> Clean, simple navigation only</li>
                    <li>• <strong>Fixed Wellness card:</strong> Now accessible at zoom levels below 125%</li>
                    <li>• <strong>Smart maxSlides calculation:</strong> Accounts for container width and zoom level</li>
                    <li>• <strong>Proper button states:</strong> Disabled when at start/end positions</li>
                </ul>
            </div>
            
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                <h2 class="text-xl font-semibold text-blue-900 mb-4">🧪 Test Instructions</h2>
                <ol class="text-blue-800 space-y-2 list-decimal list-inside">
                    <li><strong>Visit Homepage:</strong> Go to <a href="<?php echo url(''); ?>" class="underline">homepage</a></li>
                    <li><strong>Scroll to Industries:</strong> Find "Industries We Serve" section</li>
                    <li><strong>Test Normal Zoom (100%):</strong> Use arrows to navigate to Wellness card</li>
                    <li><strong>Test Low Zoom (75%):</strong> Zoom out to 75% and test navigation</li>
                    <li><strong>Test Very Low Zoom (50%):</strong> Zoom out to 50% and test navigation</li>
                    <li><strong>Verify Wellness Card:</strong> Ensure it's fully visible and accessible</li>
                </ol>
            </div>

            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
                <h2 class="text-xl font-semibold text-yellow-900 mb-4">🔧 Technical Implementation</h2>
                <div class="text-yellow-800 space-y-4">
                    <div>
                        <h3 class="font-semibold">Simplified Alpine.js Function:</h3>
                        <pre class="bg-yellow-100 p-3 rounded text-sm mt-2"><code>// Removed infinite/auto features
function industriesCarousel() {
    return {
        currentSlide: 0,
        slideWidth: 304,
        totalSlides: 10,
        slidesPerView: 4,
        
        // Simple navigation only
        nextSlide() {
            if (this.currentSlide < this.maxSlides) {
                this.currentSlide++;
            }
        },
        
        prevSlide() {
            if (this.currentSlide > 0) {
                this.currentSlide--;
            }
        }
    };
}</code></pre>
                    </div>
                    <div>
                        <h3 class="font-semibold">Smart maxSlides Calculation:</h3>
                        <pre class="bg-yellow-100 p-3 rounded text-sm mt-2"><code>get maxSlides() {
    // Calculate based on actual container width
    const containerWidth = this.$el.offsetWidth - 120;
    const visibleSlides = Math.floor(containerWidth / this.slideWidth);
    const calculatedMax = Math.max(0, this.totalSlides - visibleSlides);
    
    // Add buffer for zoom levels below 125%
    const zoomLevel = window.devicePixelRatio || 1;
    const extraBuffer = zoomLevel < 1.25 ? 2 : 0;
    
    return Math.min(this.totalSlides - 1, calculatedMax + extraBuffer);
}</code></pre>
                    </div>
                </div>
            </div>

            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">📋 Expected Behavior</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-semibold text-gray-800 mb-2">✅ Fixed Behavior:</h3>
                        <ul class="text-gray-700 space-y-1 text-sm">
                            <li>• Wellness card accessible at all zoom levels</li>
                            <li>• Right arrow works until last card is visible</li>
                            <li>• No auto-advance or infinite looping</li>
                            <li>• Clean, simple navigation</li>
                            <li>• Proper button disabled states</li>
                            <li>• Responsive to window resize</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-800 mb-2">🎯 Zoom Level Tests:</h3>
                        <ul class="text-gray-700 space-y-1 text-sm">
                            <li>• <strong>100% zoom:</strong> Standard navigation</li>
                            <li>• <strong>75% zoom:</strong> Extra buffer slides added</li>
                            <li>• <strong>50% zoom:</strong> Maximum buffer for accessibility</li>
                            <li>• <strong>All levels:</strong> Wellness card fully visible</li>
                            <li>• <strong>Responsive:</strong> Adapts to container width</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="text-center mt-8">
                <a href="<?php echo url(''); ?>" class="btn-primary mr-4">Test on Homepage</a>
                <a href="<?php echo url('carousel-debug'); ?>" class="btn-outline">Debug Page</a>
            </div>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Wellness Card Test Page Loaded');
    
    // Display current zoom level and device pixel ratio
    const zoomLevel = window.devicePixelRatio || 1;
    const windowWidth = window.innerWidth;
    
    console.log('Current browser settings:', {
        devicePixelRatio: zoomLevel,
        windowWidth: windowWidth,
        zoomBelow125: zoomLevel < 1.25,
        extraBufferNeeded: zoomLevel < 1.25 ? 'Yes' : 'No'
    });
    
    // Monitor zoom changes
    window.addEventListener('resize', function() {
        const newZoom = window.devicePixelRatio || 1;
        const newWidth = window.innerWidth;
        
        console.log('Zoom/resize detected:', {
            devicePixelRatio: newZoom,
            windowWidth: newWidth,
            zoomBelow125: newZoom < 1.25,
            extraBufferNeeded: newZoom < 1.25 ? 'Yes' : 'No'
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
